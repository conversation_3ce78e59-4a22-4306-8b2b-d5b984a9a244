import 'package:dio/dio.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:gymgo/consts.dart';

class StripeService {
  StripeService._();

  static final StripeService instance = StripeService._();

  Future<bool> makePayment(double amount) async {
    try {
      String? paymentIntentClientSecret = await _createPaymentIntent(
        amount,
        'myr', // Change to your desired currency
      );
      if (paymentIntentClientSecret == null) return false;

      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntentClientSecret,
          merchantDisplayName: 'GymGo',
        ),
      );

      await Stripe.instance.presentPaymentSheet();
      return true; // Success
    } catch (e) {
      print('Payment error: $e');
      return false; // Payment failed or canceled
    }
  }

  Future<String?> _createPaymentIntent(double amount, String currency) async {
    try {
      final Dio dio = Dio();
      Map<String, dynamic> data = {
        'amount': _calculateAmount(amount),
        'currency': currency,
      };

      var response = await dio.post(
        'https://api.stripe.com/v1/payment_intents',
        data: data,
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            "Authorization": "Bearer $stripeSecretKey",
            "Content-Type": "application/x-www-form-urlencoded",
          },
        ),
      );

      return response.data?['client_secret'];
    } catch (e) {
      print('Error creating payment intent: $e');
      return null;
    }
  }

  String _calculateAmount(double amount) {
    return (amount * 100).toInt().toString(); // Amount in cents
  }
}
