import 'package:cloud_firestore/cloud_firestore.dart';
import '../schedule/gym_class.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ScheduleService {
  final CollectionReference _classesCollection = FirebaseFirestore.instance
      .collection('gym_classes');

  // Add a new class
  Future<void> addClass(GymClass gymClass) async {
    await _classesCollection.add(gymClass.toMap());
  }

  // Get all classes
  Stream<List<GymClass>> getClasses() {
    return _classesCollection.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return GymClass.fromMap(doc.id, data);
      }).toList();
    });
  }

  // Delete a class
  Future<void> deleteClass(String classId) async {
    await _classesCollection.doc(classId).delete();
  }

  // Update a class
  Future<void> updateClass(GymClass gymClass) async {
    if (gymClass.id != null) {
      await _classesCollection.doc(gymClass.id).update(gymClass.toMap());
    }
  }

  // Book a class
  Future<void> bookClass(GymClass gymClass) async {
    if (gymClass.id != null && gymClass.capacity > 0) {
      await FirebaseFirestore.instance
          .collection('gym_classes')
          .doc(gymClass.id)
          .update({
            'capacity': gymClass.capacity - 1,
            'enrolled': gymClass.enrolled + 1,
          });
    }
  }

  // Add a class ID to the current user's bookedClasses array in Firestore
  Future<void> addBookedClassForUser(String classId) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userRef = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid);
      await userRef.update({
        'bookedClasses': FieldValue.arrayUnion([classId]),
      });
    }
  }

  // Get the set of class IDs the current user has booked
  Future<Set<String>> getBookedClassesForUser() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();
      final booked = userDoc.data()?['bookedClasses'] ?? [];
      return Set<String>.from(booked);
    }
    return {};
  }

  // Book a class for multiple children
  Future<void> bookClassForChildren(GymClass gymClass, List<String> childIds) async {
    if (gymClass.id != null && gymClass.capacity >= childIds.length) {
      await FirebaseFirestore.instance.collection('gym_classes').doc(gymClass.id).update({
        'capacity': gymClass.capacity - childIds.length,
        'enrolled': gymClass.enrolled + childIds.length,
      });
      for (String childId in childIds) {
        final childRef = FirebaseFirestore.instance
            .collection('children')
            .doc(childId);
        await childRef.update({
          'bookedClasses': FieldValue.arrayUnion([
            {
              'classId': gymClass.id,
              'className': gymClass.name,
              'payment': gymClass.payment,
              'status': 'Pending', // Default status for new bookings,
            },
          ]),
        });
      }
    }
  }

  // Get the set of class IDs a child has booked
  Future<Set<String>> getBookedClassesForChild(String childId) async {
    final childDoc = await FirebaseFirestore.instance
        .collection('children')
        .doc(childId)
        .get();
    final booked = childDoc.data()?['bookedClasses'] ?? [];
    if (booked is List) {
      return Set<String>.from(booked.map((item) => item is Map ? item['classId'] : null).where((id) => id != null));
    } else {
      return Set<String>.from([]);
    }
  }
}
