import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gymgo/service/attendance_service.dart';
import 'package:gymgo/schedule/gym_class.dart';
import 'package:intl/intl.dart';
import 'child_avatar.dart';

class FitnessJourneyWidget extends StatefulWidget {
  final String childId;
  final String childName;

  const FitnessJourneyWidget({
    required this.childId,
    required this.childName,
    super.key,
  });

  @override
  State<FitnessJourneyWidget> createState() => _FitnessJourneyWidgetState();
}

class _FitnessJourneyWidgetState extends State<FitnessJourneyWidget> {
  final AttendanceService _attendanceService = AttendanceService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Map<String, dynamic>? _childInfo;
  String _parentName = 'Unknown';
  List<Map<String, dynamic>> _attendanceRecords = [];
  Map<String, GymClass> _classDetails = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadChildData();
  }

  // Load child, parent, attendance, and class data
  Future<void> _loadChildData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get child information
      final childDoc = await _firestore.collection('children').doc(widget.childId).get();
      if (!childDoc.exists) {
        throw Exception('Child not found');
      }
      _childInfo = childDoc.data();

      // Get parent information
      final parentId = _childInfo?['userId'];
      if (parentId != null) {
        final parentDoc = await _firestore.collection('users').doc(parentId).get();
        if (parentDoc.exists && parentDoc.data() != null) {
          _parentName = parentDoc.data()!['fullName'] ?? parentDoc.data()!['name'] ?? 'Unknown';
        }
      }

      // Get attendance records (from top-level attendance collection, filter by userId)
      final attendanceSnapshot = await _firestore
          .collection('attendance')
          .where('userId', isEqualTo: widget.childId)
          .get();

      final List<Map<String, dynamic>> records = [];
      for (var doc in attendanceSnapshot.docs) {
        try {
          final data = doc.data();
          if (data != null) {
            data['id'] = doc.id;
            records.add(data);
          }
        } catch (e) {
           print('Error processing attendance document ${doc.id}: $e');
        }
      }

      // Get class details
      final Set<String> classIds = records
          .map((record) => record['classId'] as String)
          .where((id) => id != 'N/A')
          .toSet();

      for (final classId in classIds) {
        try {
          final classDoc = await _firestore.collection('gym_classes').doc(classId).get();
          if (classDoc.exists && classDoc.data() != null) {
            _classDetails[classId] = GymClass.fromMap(classDoc.id, classDoc.data()!);
          }
        } catch (e) {
           print('Error loading class details for $classId: $e');
        }
      }

      // Sort attendance records by class date (latest first)
      records.sort((a, b) {
        final classA = _classDetails[a['classId']];
        final classB = _classDetails[b['classId']];
        if (classA == null || classB == null) {
          return 0;
        }
        return classB.date.compareTo(classA.date);
      });

      setState(() {
        _attendanceRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

   // Build child's profile info card with avatar and statistics
  Widget _buildInfoCard() {
    final totalClasses = _attendanceRecords.length;
    final attendedClasses = _attendanceRecords.where((record) {
      final status = (record['status'] ?? '').toString().toLowerCase();
      return status == 'present' || status == 'late';
    }).length;
    final missedClasses = _attendanceRecords.where((record) {
      final status = (record['status'] ?? '').toString().toLowerCase();
      return status == 'absent';
    }).length;

    final avatarPath = _childInfo?['avatarPath'] as String?;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Use the reusable avatar widget
                buildChildAvatar(
                  avatarPath: avatarPath,
                  displayName: widget.childName,
                  radius: 30,
                  imageSize: 60,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.childName,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF8B5E3C),
                        ),
                      ),
                      Text(
                        'Parent: $_parentName',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Total Classes', totalClasses.toString()),
                _buildStatItem('Attended', attendedClasses.toString()),
                _buildStatItem('Missed', missedClasses.toString()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build a statistic item (Total, Attended, Missed)
  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF8B5E3C),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  // Build each timeline card for attendance record
  Widget _buildTimelineItem(Map<String, dynamic> record) {
    final String classId = record['classId'] ?? 'N/A';
    final GymClass classDetail = _classDetails[classId] ?? GymClass(
      id: classId,
      name: 'Loading Class...',
      date: DateTime.now(),
      time: 'N/A',
      duration: 'N/A',
      instructor: 'N/A',
      capacity: 0,
      enrolled: 0
    );

    final String status = (record['status'] ?? '').toString();
    final String statusText = status.isNotEmpty ? status : 'Unmarked';
    final Color statusColor = status.toLowerCase() == 'present'
        ? Colors.green
        : status.toLowerCase() == 'absent'
            ? Colors.red
            : status.toLowerCase() == 'late'
                ? Colors.orange
                : Colors.grey;
    final IconData statusIcon = status.toLowerCase() == 'present'
        ? Icons.check_circle
        : status.toLowerCase() == 'absent'
            ? Icons.cancel
            : status.toLowerCase() == 'late'
                ? Icons.access_time
                : Icons.help_outline;

    String formattedDate = 'N/A';
    try {
      formattedDate = DateFormat('MMM dd, yyyy').format(classDetail.date);
    } catch (e) {
      // print('Error formatting date: $e');
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 2,
              height: 80,
              color: statusColor,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(statusIcon, color: statusColor, size: 24),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          classDetail.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$formattedDate at ${classDetail.time}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Instructor: ${classDetail.instructor} • Duration: ${classDetail.duration}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Status: $statusText',
                          style: TextStyle(
                            fontSize: 12,
                            color: statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (record['note'] != null && record['note'].toString().isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Note: ${record['note']}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Text(
          'Error: $_error',
          style: const TextStyle(color: Colors.red),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.childName}\'s Journey'),
        backgroundColor: const Color(0xFF8B5E3C),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Fitness Journey Timeline',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF8B5E3C),
                ),
              ),
            ),
            if (_attendanceRecords.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'No attendance records found.',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _attendanceRecords.length,
                itemBuilder: (context, index) => _buildTimelineItem(_attendanceRecords[index]),
              ),
          ],
        ),
      ),
    );
  }
}
