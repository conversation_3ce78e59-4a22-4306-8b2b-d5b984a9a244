import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class AttendanceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Cache for leaderboard data
  List<Map<String, dynamic>>? _leaderboardCache;
  DateTime? _lastCacheTime;
  static const Duration _cacheDuration = Duration(minutes: 5);

  Future<List<Map<String, dynamic>>> getChildrenAttendanceCounts() async {
    if (_leaderboardCache != null && _lastCacheTime != null) {
      final cacheAge = DateTime.now().difference(_lastCacheTime!);
      if (cacheAge < _cacheDuration) {
        return _leaderboardCache!;
      }
    }

    try {
      // Get all children documents in a single query
      final childrenSnapshot = await _firestore
          .collection('children')
          .get();

      // Get all attendance documents in a single query with filter
      final attendanceSnapshot = await _firestore
          .collection('attendance')
          .where('status', isEqualTo: 'Present')
          .get();

      // Create a map of child IDs to attendance counts
      final Map<String, int> attendanceCountMap = {};
      for (var doc in attendanceSnapshot.docs) {
        final data = doc.data();
        final childIdFromAttendance = data['userId'];
        if (childIdFromAttendance != null) {
          attendanceCountMap[childIdFromAttendance] = 
              (attendanceCountMap[childIdFromAttendance] ?? 0) + 1;
        }
      }

      // Combine children data with attendance counts and calculate total booked classes
      final List<Map<String, dynamic>> leaderboard = [];
      
      for (var doc in childrenSnapshot.docs) {
        final childData = doc.data();
        final childId = doc.id;
        
        // Count total booked classes
        int totalBookedClasses = 0;
        final bookedClasses = childData['bookedClasses'];
        if (bookedClasses is List) {
          totalBookedClasses = bookedClasses.length;
        }
        
        // Ensure at least 1 to avoid division by zero
        if (totalBookedClasses == 0) totalBookedClasses = 1;
        
        leaderboard.add({
          'childId': childId,
          'fullName': childData['fullName'] ?? 'Unknown',
          'avatarPath': childData['avatarPath'] ?? 'assets/children_avatar/child.png',
          'attendanceCount': attendanceCountMap[childId] ?? 0,
          'totalBookedClasses': totalBookedClasses,
        });
      }

      // Sort by attendance count in descending order
      leaderboard.sort((a, b) => (b['attendanceCount'] as int).compareTo(a['attendanceCount'] as int));

      // Update cache
      _leaderboardCache = leaderboard;
      _lastCacheTime = DateTime.now();

      return leaderboard;
    } catch (e) {
      print('Error getting attendance counts: $e');
      // If there's an error, return cached data if available
      if (_leaderboardCache != null) {
        return _leaderboardCache!;
      }
      rethrow;
    }
  }

  // Clear cache when needed (e.g., when new attendance is marked)
  void clearCache() {
    _leaderboardCache = null;
    _lastCacheTime = null;
  }

  // Mark attendance for a class
  Future<void> markAttendance(String classId, bool attended) async {
    final user = _auth.currentUser;
    if (user == null) return;

    final attendanceRef = _firestore
        .collection('attendance')
        .doc('${user.uid}_$classId');

    await attendanceRef.set({
      'userId': user.uid,
      'classId': classId,
      'status': attended ? 'Present' : 'Absent',
      'timestamp': FieldValue.serverTimestamp(),
    });

    // Clear cache when new attendance is marked
    clearCache();
  }

  // Get attendance status for a class
  Future<bool?> getAttendanceStatus(String classId) async {
    final user = _auth.currentUser;
    if (user == null) return null;

    final attendanceDoc = await _firestore
        .collection('attendance')
        .doc('${user.uid}_$classId')
        .get();

    return attendanceDoc.data()?['status'] == 'Present';
  }

  // Get all attendance records for a list of user IDs (for parent view)
  Stream<Map<String, List<Map<String, dynamic>>>> getAttendanceForUsers(List<String> userIds) {
    if (userIds.isEmpty) return Stream.value({});

    return _firestore
        .collection('attendance')
        .where('userId', whereIn: userIds)
        .snapshots()
        .map((snapshot) {
      final Map<String, List<Map<String, dynamic>>> attendanceMap = {};
      for (var doc in snapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String;
        attendanceMap.putIfAbsent(userId, () => []);
        attendanceMap[userId]!.add(data);
      }
      return attendanceMap;
    });
  }

  // Get all attendance records for the current user
  Stream<Map<String, bool>> getUserAttendance() {
    final user = _auth.currentUser;
    if (user == null) return Stream.value({});

    return _firestore
        .collection('attendance')
        .where('userId', isEqualTo: user.uid)
        .snapshots()
        .map((snapshot) {
      final Map<String, bool> attendanceMap = {};
      for (var doc in snapshot.docs) {
        final data = doc.data();
        attendanceMap[data['classId'] as String] = data['status'] == 'Present';
      }
      return attendanceMap;
    });
  }

  // Retrieves all children who have booked the given class, along with their parent information.
  //  Iterates through all users (parents), and for each parent, checks each child's bookedClasses.
  // If a child has booked the specified class, their info and their parent's info are added to the result.

  // Returns a list of maps: {childId, childName, parentId, parentName}
  Future<List<Map<String, dynamic>>> getChildrenBookedForClass(String classId) async {
    final childrenSnapshot = await _firestore.collection('children').get();
    List<Map<String, dynamic>> result = [];
    for (var childDoc in childrenSnapshot.docs) {
      final childData = childDoc.data();
      final bookedClasses = childData['bookedClasses'];
      bool hasBooked = false;
      if (bookedClasses is List) {
        for (var booking in bookedClasses) {
          if (booking is Map && booking.containsKey('classId') && booking['classId'] == classId) {
            hasBooked = true;
            break;
          }
        }
      }
      if (hasBooked) {
        final userId = childData['userId'];
        String parentName = 'Unknown Parent';
        if (userId != null) {
          final parentDoc = await _firestore.collection('users').doc(userId).get();
          if (parentDoc.exists && parentDoc.data() != null) {
            parentName = parentDoc.data()!['name'] ?? 'Unknown Parent';
          }
        }
        result.add({
          'childId': childDoc.id,
          'childName': childData['fullName'] ?? 'Unknown Child',
          'parentId': userId ?? 'Unknown Parent',
          'parentName': parentName,
        });
      }
    }
    return result;
  }
} 
