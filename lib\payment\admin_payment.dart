//admin_payment.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

class AdminPaymentScreen extends StatefulWidget {
  final String classId;
  final String className;
  final String classPayment;

  const AdminPaymentScreen({
    super.key,
    required this.classId,
    required this.className,
    required this.classPayment,
  });

  @override
  _AdminPaymentScreenState createState() => _AdminPaymentScreenState();
}

class _AdminPaymentScreenState extends State<AdminPaymentScreen>
    with SingleTickerProviderStateMixin {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<List<Map<String, dynamic>>> _fetchEnrolledChildren() async {
    try {
      List<Map<String, dynamic>> enrolledChildren = [];

      // Get all children from the separate children collection
      final childrenSnapshot = await _firestore.collection('children').get();

      for (var childDoc in childrenSnapshot.docs) {
        final childData = childDoc.data() as Map<String, dynamic>;
        final bookedClasses =
            childData['bookedClasses'] as List<dynamic>? ?? [];
        final userId = childData['userId'] as String?;

        if (userId == null) continue;

        // Get parent information from users collection
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData =
            userDoc.exists ? userDoc.data() as Map<String, dynamic> : {};

        // Check if this child is enrolled in the selected class
        for (var bookedClass in bookedClasses) {
          // Add type checking to handle both Map and String cases
          Map<String, dynamic>? bookedClassMap;

          if (bookedClass is Map<String, dynamic>) {
            bookedClassMap = bookedClass;
          } else if (bookedClass is String) {
            // If it's a string, skip this entry or try to handle it
            print("Warning: bookedClass is a String: $bookedClass");
            continue;
          } else {
            print(
              "Warning: bookedClass is neither Map nor String: ${bookedClass.runtimeType}",
            );
            continue;
          }

          // Check if this booking matches our class
          if (bookedClassMap['classId'] == widget.classId) {
            enrolledChildren.add({
              'childId': childDoc.id,
              'fullName': childData['fullName'] ?? 'Unknown Child',
              'email': userData['email'] ?? childData['email'] ?? 'No email',
              'payment': bookedClassMap['payment'] ?? 0,
              'status': bookedClassMap['status'] ?? 'Pending',
              'className': bookedClassMap['className'] ?? widget.className,
              'parentId': userId,
              'parentName': userData['name'] ?? 'Unknown Parent',
              'gender': childData['gender'] ?? 'Not specified',
              'createdAt': childData['createdAt'],
              'bookingDate': bookedClassMap['bookingDate'] ?? 'Not specified',
              'age': childData['age'] ?? 'Not specified',
              'phone': userData['phone'] ?? childData['phone'] ?? 'No phone',
              'bookedClasses': [bookedClassMap], // Include the specific booking
            });
            break;
          }
        }
      }

      // Sort by child name
      enrolledChildren.sort(
        (a, b) => (a['fullName'] as String).compareTo(b['fullName'] as String),
      );

      print("Total enrolled children found: ${enrolledChildren.length}");
      return enrolledChildren;
    } catch (e) {
      print("Error fetching enrolled children: $e");
      return [];
    }
  }

  Future<void> _updatePaymentStatus(
    Map<String, dynamic> child,
    String newStatus,
  ) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFF8B5E3C),
                    ),
                  ),
                  SizedBox(width: 16),
                  Text('Updating status...'),
                ],
              ),
            ),
      );

      // Get the child document from the children collection
      final childDoc =
          await _firestore.collection('children').doc(child['childId']).get();

      if (childDoc.exists) {
        final childData = childDoc.data() as Map<String, dynamic>;
        final bookedClasses = List<Map<String, dynamic>>.from(
          (childData['bookedClasses'] ?? []).where(
            (item) => item is Map<String, dynamic>,
          ),
        );

        // Find and update the specific class booking
        for (int i = 0; i < bookedClasses.length; i++) {
          if (bookedClasses[i]['classId'] == widget.classId) {
            bookedClasses[i]['status'] = newStatus;
            break;
          }
        }

        // Update the document in the children collection
        await _firestore.collection('children').doc(child['childId']).update({
          'bookedClasses': bookedClasses,
        });

        // Close loading dialog
        Navigator.of(context).pop();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment status updated to $newStatus'),
            // backgroundColor: Colors.green,
          ),
        );

        // Refresh the data
        setState(() {});
      }
    } catch (e) {
      // Close loading dialog
      Navigator.of(context).pop();

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating status: $e'),
          // backgroundColor: Colors.red,
        ),
      );
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.red;
      case 'cancelled':
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showStatusUpdateDialog(Map<String, dynamic> child) {
    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: Text('Update Payment Status'),
            content: Text('Update payment status for ${child['fullName']}?'),
            actions: [
              Row(
                mainAxisAlignment:
                    MainAxisAlignment.spaceEvenly, // Evenly spaced buttons
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(ctx);
                      await _updatePaymentStatus(child, 'Paid');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Mark Paid'),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(ctx);
                      await _updatePaymentStatus(child, 'Pending');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Mark Pending'),
                  ),
                ],
              ),
              SizedBox(height: 10), // Add spacing between rows
              // Center(
              //   child: TextButton(
              //     onPressed: () => Navigator.pop(ctx),
              //     child: Text('Cancel', style: TextStyle(color: Colors.grey)),
              //   ),
              // ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(ctx);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color.fromARGB(255, 211, 169, 137),
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Cancel'),
                  ),
                ],
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: Text(
          'Payment - ${widget.className}',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Class Info Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Class Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF8B5E3C),
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.class_, size: 16, color: Color(0xFF8B5E3C)),
                    SizedBox(width: 8),
                    Text('Class: ${widget.className}'),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Color(0xFF8B5E3C),
                    ),
                    SizedBox(width: 8),
                    Text('Payment Amount: RM${widget.classPayment}'),
                  ],
                ),
              ],
            ),
          ),

          // Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by child name or parent name...',
                prefixIcon: const Icon(Icons.search, color: Color(0xFF8B5E3C)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                ),
                fillColor: Colors.white,
                filled: true,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          SizedBox(height: 16),

          // Children List
          Expanded(
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: _fetchEnrolledChildren(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF8B5E3C),
                      ),
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error, size: 64, color: Colors.red),
                        SizedBox(height: 16),
                        Text(
                          'Error loading data',
                          style: TextStyle(fontSize: 18, color: Colors.red),
                        ),
                        SizedBox(height: 8),
                        Text('${snapshot.error}'),
                      ],
                    ),
                  );
                }

                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 100,
                          color: Color(0xFF8B5E3C),
                        ),
                        SizedBox(height: 24),
                        Text(
                          'No children enrolled',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                        Text(
                          'No children are enrolled in this class yet.',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                final children = snapshot.data!;
                final filteredChildren =
                    children.where((child) {
                      final childName =
                          (child['fullName'] ?? '').toString().toLowerCase();
                      final parentName =
                          (child['parentName'] ?? '').toString().toLowerCase();
                      final query = _searchQuery.toLowerCase();
                      return childName.contains(query) ||
                          parentName.contains(query);
                    }).toList();

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredChildren.length,
                  itemBuilder: (context, index) {
                    final child = filteredChildren[index];
                    final fullName = child['fullName'];
                    final parentName = child['parentName'] ?? 'Unknown Parent';
                    final bookedClasses =
                        child['bookedClasses'] as List<dynamic>;
                    final booking =
                        bookedClasses.isNotEmpty ? bookedClasses[0] : {};
                    final status = booking['status'] ?? 'Unknown';
                    final payment = booking['payment'] ?? 0;

                    return Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 6,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: () => _showStatusUpdateDialog(child),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Colors.white, Colors.grey.shade50],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top Row - Avatar, Name, and Status
                              Row(
                                children: [
                                  // Avatar
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Color(0xFF8B5E3C).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(25),
                                      border: Border.all(
                                        color: Color(
                                          0xFF8B5E3C,
                                        ).withOpacity(0.3),
                                        width: 2,
                                      ),
                                    ),
                                    child: Icon(
                                      child['gender']?.toLowerCase() == 'male'
                                          ? Icons.boy
                                          : child['gender']?.toLowerCase() ==
                                              'female'
                                          ? Icons.girl
                                          : Icons.child_care,
                                      color: Color(0xFF8B5E3C),
                                      size: 24,
                                    ),
                                  ),
                                  SizedBox(width: 16),

                                  // Name and Parent Info
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          fullName,
                                          style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF8B5E3C),
                                          ),
                                        ),
                                        SizedBox(height: 4),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.person_outline,
                                              size: 14,
                                              color: Colors.grey[600],
                                            ),
                                            SizedBox(width: 4),
                                            Text(
                                              'Parent: $parentName',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Status Badge
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          status == "Paid"
                                              ? Colors.green[100]
                                              : Colors.red[100],
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color:
                                            status == "Paid"
                                                ? Colors.green[300]!
                                                : Colors.red[300]!,
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      status,
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                        color:
                                            status == "Paid"
                                                ? Colors.green[800]
                                                : Colors.red[800],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              // SizedBox(height: 16),

                              // // Divider
                              // Container(
                              //   height: 1,
                              //   width: double.infinity,
                              //   color: Colors.grey[300],
                              // ),

                              // SizedBox(height: 16),

                              // Class and Payment Information
                              // Row(
                              //   children: [
                              //     // Class Info
                              //     Expanded(
                              //       child: Container(
                              //         padding: const EdgeInsets.all(12),
                              //         decoration: BoxDecoration(
                              //           color: Color(
                              //             0xFF8B5E3C,
                              //           ).withOpacity(0.05),
                              //           borderRadius: BorderRadius.circular(12),
                              //           border: Border.all(
                              //             color: Color(
                              //               0xFF8B5E3C,
                              //             ).withOpacity(0.2),
                              //           ),
                              //         ),
                              //         child: Column(
                              //           crossAxisAlignment:
                              //               CrossAxisAlignment.start,
                              //           children: [
                              //             Row(
                              //               children: [
                              //                 Icon(
                              //                   Icons.school,
                              //                   size: 16,
                              //                   color: Color(0xFF8B5E3C),
                              //                 ),
                              //                 SizedBox(width: 6),
                              //                 Text(
                              //                   'Class',
                              //                   style: TextStyle(
                              //                     fontSize: 12,
                              //                     color: Colors.grey[600],
                              //                     fontWeight: FontWeight.w500,
                              //                   ),
                              //                 ),
                              //               ],
                              //             ),
                              //             SizedBox(height: 4),
                              //             Text(
                              //               booking['className'] ??
                              //                   widget.className,
                              //               style: const TextStyle(
                              //                 fontSize: 16,
                              //                 fontWeight: FontWeight.w600,
                              //                 color: Color(0xFF8B5E3C),
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //       ),
                              //     ),

                              //     SizedBox(width: 12),

                              //     // Payment Info
                              //     Expanded(
                              //       child: Container(
                              //         padding: const EdgeInsets.all(12),
                              //         decoration: BoxDecoration(
                              //           color: Colors.green.withOpacity(0.05),
                              //           borderRadius: BorderRadius.circular(12),
                              //           border: Border.all(
                              //             color: Colors.green.withOpacity(0.2),
                              //           ),
                              //         ),
                              //         child: Column(
                              //           crossAxisAlignment:
                              //               CrossAxisAlignment.start,
                              //           children: [
                              //             Row(
                              //               children: [
                              //                 Icon(
                              //                   Icons.attach_money,
                              //                   size: 16,
                              //                   color: Colors.green[700],
                              //                 ),
                              //                 SizedBox(width: 6),
                              //                 Text(
                              //                   'Payment',
                              //                   style: TextStyle(
                              //                     fontSize: 12,
                              //                     color: Colors.grey[600],
                              //                     fontWeight: FontWeight.w500,
                              //                   ),
                              //                 ),
                              //               ],
                              //             ),
                              //             SizedBox(height: 4),
                              //             Text(
                              //               'RM$payment',
                              //               style: TextStyle(
                              //                 fontSize: 16,
                              //                 fontWeight: FontWeight.w600,
                              //                 color: Colors.green[700],
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //       ),
                              //     ),
                              //   ],
                              // ),

                              // Additional Info Row (Age, Phone, etc.)
                              //   if (child['age'] != null &&
                              //       child['age'] != 'Not specified')
                              //     Padding(
                              //       padding: const EdgeInsets.only(top: 12),
                              //       child: Row(
                              //         children: [
                              //           Icon(
                              //             Icons.cake,
                              //             size: 14,
                              //             color: Colors.grey[600],
                              //           ),
                              //           SizedBox(width: 6),
                              //           Text(
                              //             "Age: ${child['age']}",
                              //             style: TextStyle(
                              //               fontSize: 12,
                              //               color: Colors.grey[600],
                              //             ),
                              //           ),
                              //           if (child['phone'] != null &&
                              //               child['phone'] != 'No phone') ...[
                              //             SizedBox(width: 16),
                              //             Icon(
                              //               Icons.phone,
                              //               size: 14,
                              //               color: Colors.grey[600],
                              //             ),
                              //             SizedBox(width: 6),
                              //             Text(
                              //               child['phone'],
                              //               style: TextStyle(
                              //                 fontSize: 12,
                              //                 color: Colors.grey[600],
                              //               ),
                              //             ),
                              //           ],
                              //         ],
                              //       ),
                              //     ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
