import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gymgo/Service/attendance_service.dart';
import 'package:gymgo/schedule/gym_class.dart'; // Assuming GymClass model is available
import 'package:intl/intl.dart';
import 'package:gymgo/widgets/child_avatar.dart';

class UserHomeContent extends StatefulWidget {
  const UserHomeContent({super.key});

  @override
  State<UserHomeContent> createState() => _UserHomeContentState();
}

class _UserHomeContentState extends State<UserHomeContent> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AttendanceService _attendanceService = AttendanceService();

  User? _currentUser;
  bool _isParent = false;
  List<String> _childrenUserIds = [];
  Map<String, List<Map<String, dynamic>>> _childrenAttendance = {};
  Map<String, GymClass> _classDetails = {};
  Map<String, String> _childNames = {};

  List<Map<String, dynamic>> _attendedClasses = [];
  List<Map<String, dynamic>> _missedClasses = [];

  bool _isLoading = true;
  String? _error;

  String? _selectedChildId;

  List<Map<String, dynamic>> _children = [];

  int _totalChildren = 0;
  int _totalClasses = 0;
  int _totalAttendances = 0;
  int _totalMissedClasses = 0;
  int _todayClasses = 0;

  @override
  void initState() {
    super.initState();
    _loadChildrenDocs();
    _loadUserDataAndAttendance();
  }

  Future<void> _loadChildrenDocs() async {
    final user = _auth.currentUser;
    if (user == null) return;
    final childrenSnapshot = await _firestore
        .collection('children')
        .where('userId', isEqualTo: user.uid)
        .get();
    setState(() {
      _children = childrenSnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  Future<void> _loadUserDataAndAttendance() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _currentUser = _auth.currentUser;
      if (_currentUser == null) {
        setState(() {
          _error = 'User not logged in.';
          _isLoading = false;
        });
        return;
      }

      final userDoc = await _firestore.collection('users').doc(_currentUser!.uid).get();
      if (!userDoc.exists) {
         setState(() {
          _error = 'User data not found.';
          _isLoading = false;
        });
        return;
      }

      final userData = userDoc.data();
      if (userData != null) {
        _isParent = true;
        final childrenSnapshot = await _firestore.collection('children').where('userId', isEqualTo: _currentUser!.uid).get();
        _childrenUserIds = childrenSnapshot.docs.map((doc) => doc.id).toList();
        
        _totalChildren = _childrenUserIds.length;

        final Map<String, String> names = {};
        for (var doc in childrenSnapshot.docs) {
          names[doc.id] = doc.data()['fullName'] ?? doc.data()['name'] ?? doc.data()['childName'] ?? 'Unknown Child';
        }
        setState(() {
          _childNames = names;
        });

        _attendanceService.getAttendanceForUsers(_childrenUserIds).listen((attendanceData) async {
          print('DEBUG: Received attendance data:');
          attendanceData.forEach((k, v) => print('  $k: $v'));
          setState(() {
            _childrenAttendance = attendanceData;
          });

          int totalClasses = 0;
          int totalAttendances = 0;
          int totalMissed = 0;
          int todayClasses = 0;

          DateTime now = DateTime.now();
          String todayStr = "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

          for (var childAttendance in attendanceData.values) {
            for (var record in childAttendance) {
              totalClasses++;
              final status = (record['status'] ?? '').toString().toLowerCase();
              if (status == 'present' || status == 'late') {
                totalAttendances++;
              } else if (status == 'absent') {
                totalMissed++;
              }
              if (record['date'] != null) {
                DateTime? classDate;
                if (record['date'] is Timestamp) {
                  classDate = (record['date'] as Timestamp).toDate();
                } else if (record['date'] is String) {
                  try {
                    classDate = DateTime.parse(record['date']);
                  } catch (_) {}
                } else if (record['date'] is DateTime) {
                  classDate = record['date'];
                }
                if (classDate != null) {
                  String classDateStr = "${classDate.year}-${classDate.month.toString().padLeft(2, '0')}-${classDate.day.toString().padLeft(2, '0')}";
                  if (classDateStr == todayStr) {
                    todayClasses++;
                  }
                }
              }
            }
          }

          setState(() {
            _totalClasses = totalClasses;
            _totalAttendances = totalAttendances;
            _totalMissedClasses = totalMissed;
            _todayClasses = todayClasses;
          });

          await _fetchClassDetails();
          _filterAttendance();
          setState(() {
            _isLoading = false;
          });
        });
      } else {
        _isParent = false;
        setState(() {
          _isLoading = false;
        });
      }

    } catch (e) {
      setState(() {
        _error = 'Error loading data: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchChildNames() async {
      if (_childrenUserIds.isEmpty) return;
      final Map<String, String> names = {};
      for (final userId in _childrenUserIds) {
         try {
            final childDoc = await _firestore.collection('children').doc(userId).get();
            if (childDoc.exists && childDoc.data() != null) {
               names[userId] = childDoc.data()!['name'] ?? childDoc.data()!['fullName'] ?? childDoc.data()!['childName'] ?? 'Unknown Child';
            } else {
               names[userId] = 'Unknown Child';
            }
         } catch (e) {
            print('Error fetching child name for $userId: $e');
            names[userId] = 'Error getting name';
         }
      }
      setState(() {
         _childNames = names;
      });
  }

  Future<void> _fetchClassDetails() async {
    if (_childrenAttendance.isEmpty) return;

    final Set<String> classIds = _childrenAttendance.values
        .expand((list) => list)
        .map((record) => record['classId'] as String)
        .toSet();
    final Map<String, GymClass> details = {};

    for (final classId in classIds) {
      if (classId != 'N/A') {
        try {
          final classDoc = await _firestore.collection('gym_classes').doc(classId).get();
          if (classDoc.exists && classDoc.data() != null) {
             details[classId] = GymClass.fromMap(classDoc.id, classDoc.data()!); 
          } else {
            details[classId] = GymClass(id: classId, name: 'Unknown Class', date: DateTime.now(), time: 'N/A', duration: 'N/A', instructor: 'N/A', capacity: 0, enrolled: 0);
          }
        } catch (e) {
           print('Error fetching class details for $classId: $e');
            details[classId] = GymClass(id: classId, name: 'Error loading class', date: DateTime.now(), time: 'N/A', duration: 'N/A', instructor: 'N/A', capacity: 0, enrolled: 0);
        }
      }
    }
    setState(() {
      _classDetails = details;
    });
  }

  void _filterAttendance() {
    final childAttendanceList = _selectedChildId == null
        ? _childrenAttendance.values.expand((list) => list).toList()
        : (_childrenAttendance[_selectedChildId] ?? []);

    // Only keep records where status is not empty/null (admin has marked attendance)
    final markedAttendanceList = childAttendanceList.where((record) {
      final status = (record['status'] ?? '').toString().trim();
      return status.isNotEmpty;
    }).toList();

    print('DEBUG: Marked attendance list:');
    for (var record in markedAttendanceList) {
      print('  ${record['classId']} | ${record['date']} | status: ${record['status']} | updatedAt: ${record['updatedAt']} | createdAt: ${record['createdAt']}');
    }

    // Deduplicate: keep only the latest record per userId+classId+date
    final Map<String, Map<String, dynamic>> uniqueAttendance = {};
    for (var record in markedAttendanceList) {
      final classId = record['classId'];
      final userId = record['userId'];
      final date = (record['date'] is Timestamp)
          ? (record['date'] as Timestamp).toDate().toIso8601String().split('T').first
          : '';
      final key = '$userId|$classId|$date';
      final currentTimestamp = record['updatedAt'] ?? record['createdAt'] ?? Timestamp.now();
      if (!uniqueAttendance.containsKey(key) ||
          (currentTimestamp is Timestamp &&
            (uniqueAttendance[key]?['updatedAt'] ?? uniqueAttendance[key]?['createdAt'] ?? Timestamp.now()) is Timestamp &&
            currentTimestamp.compareTo(uniqueAttendance[key]?['updatedAt'] ?? uniqueAttendance[key]?['createdAt'] ?? Timestamp.now()) > 0)) {
        uniqueAttendance[key] = record;
        print('DEBUG: Keeping record for $key with status ${record['status']}');
      }
    }
    final dedupedAttendanceList = uniqueAttendance.values.toList();

    final filteredAttended = dedupedAttendanceList.where((record) {
      final status = (record['status'] ?? '').toString().toLowerCase();
      return status == 'present' || status == 'late';
    }).toList();
    final filteredMissed = dedupedAttendanceList.where((record) {
      final status = (record['status'] ?? '').toString().toLowerCase();
      return status == 'absent';
    }).toList();

    _attendedClasses = filteredAttended;
    _missedClasses = filteredMissed;
  }

  Widget _buildChildSelector() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            const Icon(Icons.child_care, color: Color(0xFF8B5E3C)),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButton<String>(
                value: _selectedChildId,
                hint: const Text('Select a child'),
                isExpanded: true,
                items: _childrenUserIds.map((childId) {
                  final childName = _childNames[childId] ?? 'Unknown Child';
                  // Find the child doc from _children to get avatarPath
                  final childDoc = _children.firstWhere(
                    (child) => child['id'] == childId, // 'child' represents a child document from Firestore
                    orElse: () => {},
                  );
                  final avatarPath = childDoc['avatarPath'] as String?;
                  return DropdownMenuItem<String>(
                    value: childId,
                    child: Row(
                      children: [
                        // Show avatar in dropdown
                        buildChildAvatar(
                          avatarPath: avatarPath,
                          displayName: childName,
                          radius: 16,
                          imageSize: 32,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          childName,
                          style: const TextStyle(fontSize: 16, color: Color(0xFF8B5E3C)),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                selectedItemBuilder: (context) {
                  // Only show the child's name (no avatar) in the selected area
                  return _childrenUserIds.map((childId) {
                    final childName = _childNames[childId] ?? 'Unknown Child';
                    return Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        childName,
                        style: const TextStyle(fontSize: 16, color: Color(0xFF8B5E3C)),
                      ),
                    );
                  }).toList();
                },
                onChanged: (value) {
                  setState(() {
                    _selectedChildId = value;
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceItem(Map<String, dynamic> attendanceRecord) {
    final String classId = attendanceRecord['classId'] ?? 'N/A';
    final String userId = attendanceRecord['userId'] ?? 'N/A';
    final GymClass classDetail = _classDetails[classId] ?? GymClass(
      id: classId, name: 'Loading Class...', date: DateTime.now(),
      time: 'N/A', duration: 'N/A', instructor: 'N/A', capacity: 0, enrolled: 0
    );
    final String childName = _childNames[userId] ?? 'Loading Child...';
    final String status = (attendanceRecord['status'] ?? '').toString();
    final String statusText = status.isNotEmpty ? status : 'Unmarked';
    final Color statusColor = status.toLowerCase() == 'present'
        ? Colors.green
        : status.toLowerCase() == 'absent'
            ? Colors.red
            : status.toLowerCase() == 'late'
                ? Colors.orange
                : Colors.grey;
    final IconData statusIcon = status.toLowerCase() == 'present'
        ? Icons.check_circle
        : status.toLowerCase() == 'absent'
            ? Icons.cancel
            : status.toLowerCase() == 'late'
                ? Icons.access_time
                : Icons.help_outline;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    classDetail.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$childName • ${classDetail.date.day}/${classDetail.date.month}/${classDetail.date.year} at ${classDetail.time}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  if (status.toLowerCase() != 'absent' && attendanceRecord['checkInTime'] != null)
                    Text(
                      'Check-in Time: ' +
                        (attendanceRecord['checkInTime'] is String
                          ? attendanceRecord['checkInTime']
                          : (attendanceRecord['checkInTime'] is Timestamp
                              ? DateFormat('hh:mm a').format((attendanceRecord['checkInTime'] as Timestamp).toDate())
                              : (attendanceRecord['checkInTime'] is DateTime
                                  ? DateFormat('hh:mm a').format(attendanceRecord['checkInTime'])
                                  : '')
                            )
                        ),
                      style: const TextStyle(fontSize: 13, color: Colors.black54),
                    ),
                  Text(
                    'Attendance: $statusText',
                    style: TextStyle(
                      fontSize: 14,
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCard(IconData icon, String title, String value, Color bgColor) {
    return Container(
      width:160,
      height: 120,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: const Color(0xFF8B5E3C)),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSquareOverviewCard(IconData icon, String title, String value, Color bgColor) {
    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 28, color: const Color(0xFF8B5E3C)),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(child: Text('Error: $_error', style: TextStyle(color: Colors.red), textAlign: TextAlign.center));
    }

    if (_isParent) {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Parent Dashboard',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Color(0xFF8B5E3C)),
                    onPressed: _loadUserDataAndAttendance,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildOverviewCard(
                    Icons.cancel,
                    'Total Missed Classes',
                    '$_totalMissedClasses',
                    const Color.fromARGB(255, 255, 215, 159),
                  ),
                  _buildOverviewCard(
                    Icons.fitness_center,
                    'Total Classes',
                    '$_totalClasses',
                    const Color(0xFFE0C3A3),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildOverviewCard(
                    Icons.check_circle,
                    'Total Attendances',
                    '$_totalAttendances',
                    const Color(0xFFD4E4C5),
                  ),
                  _buildOverviewCard(
                    Icons.today,
                    "Today's Classes",
                    '$_todayClasses',
                    const Color(0xFFF8D7DA),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const Text(
                'Fitness Progress',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF8B5E3C),
                ),
              ),
              SizedBox(height: 16),
              _buildChildSelector(),
              const SizedBox(height: 16),
              _selectedChildId == null
                ? const SizedBox.shrink()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildSquareOverviewCard(
                        Icons.history,
                        'Past Classes',
                        '${_attendedClasses.length + _missedClasses.length}',
                        const Color(0xFFE0C3A3),
                      ),
                      _buildSquareOverviewCard(
                        Icons.check_circle_outline,
                        'Classes Attended',
                        '${_attendedClasses.length}',
                        const Color(0xFFD4E4C5),
                      ),
                      _buildSquareOverviewCard(
                        Icons.cancel_outlined,
                        'Classes Missed',
                        '${_missedClasses.length}',
                        const Color(0xFFF8D7DA),
                      ),
                    ],
                  ),
              const SizedBox(height: 24),

              const Text(
                'Attendance',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF8B5E3C),
                ),
              ),
              const SizedBox(height: 16),

               if (_childrenUserIds.isEmpty)
                const Center(child: Text('No children linked to this parent account.', style: TextStyle(color: Colors.grey)))
               else if (_selectedChildId == null)
                 const Center(child: Text('Please select a child to view attendance.', style: TextStyle(color: Colors.grey)))
               else if (_attendedClasses.isEmpty)
                 const Center(child: Text('No attended classes to display.', style: TextStyle(color: Colors.grey)))
               else ...[
                  const Text(
                    'Attended Classes',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Column(
                    children: _attendedClasses
                        .where((record) => _selectedChildId == null || record['userId'] == _selectedChildId)
                        .map((record) => _buildAttendanceItem(record))
                        .toList(),
                  ),

                  const SizedBox(height: 24),

                  const Text(
                    'Missed Classes',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_missedClasses.isEmpty)
                     const Center(child: Text('No missed classes to display.', style: TextStyle(color: Colors.grey)))
                  else
                    Column(
                      children: _missedClasses
                          .where((record) => _selectedChildId == null || record['userId'] == _selectedChildId)
                          .map((record) => _buildAttendanceItem(record))
                          .toList(),
                    ),
               ]
            ],
          ),
        ),
      );
    } else {
      return const Center(child: Text('Home Content for users without linked children.'));
    }
  }
} 