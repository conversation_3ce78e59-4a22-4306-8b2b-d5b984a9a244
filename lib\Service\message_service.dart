import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class MessageService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  Map<String, Map<String, dynamic>> _userCache = {};

  // Helper to get a unique chat ID for two users
  String _getChatId(String user1Id, String user2Id) {
    final List<String> ids = [user1Id, user2Id];
    ids.sort();
    return ids.join('_');
  }

  // Public method to get chat ID
  Future<String> getChatId(String user1Id, String user2Id) async {
    return _getChatId(user1Id, user2Id);
  }

  // Send a message
  Future<void> sendMessage({
    required String receiverId,
    required String text,
    String? fileUrl,
    String? fileName,
    String? fileType,
    String? stickerPath,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    final senderId = currentUser.uid;
    final chatId = _getChatId(senderId, receiverId);

    final message = {
      'senderId': senderId,
      'receiverId': receiverId,
      'text': text,
      'timestamp': FieldValue.serverTimestamp(),
      if (fileUrl != null) 'fileUrl': fileUrl,
      if (fileName != null) 'fileName': fileName,
      if (fileType != null) 'fileType': fileType,
      if (stickerPath != null) 'stickerPath': stickerPath,
    };

    // Add message to the chat subcollection
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .add(message);

    // Update chat summary with last message info
    final chatSummary = {
      'lastMessage': text.isNotEmpty ? text : (stickerPath != null ? 'Sent a sticker' : 'Sent a file'),
      'lastMessageTimestamp': FieldValue.serverTimestamp(),
      'participants': [senderId, receiverId],
      'unreadCounts': {
        receiverId: FieldValue.increment(1), // Increment unread count for receiver
        senderId: 0, // Reset unread count for sender
      },
    };

    await _firestore.collection('chats').doc(chatId).set(chatSummary, SetOptions(merge: true));
  }

  // Get messages for a specific chat
  Stream<QuerySnapshot> getMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp')
        .snapshots();
  }

  // Get conversations for the current user
  Stream<List<DocumentSnapshot<Map<String, dynamic>>>> getConversations() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    return _firestore
        .collection('chats')
        .where('participants', arrayContains: currentUser.uid)
        .orderBy('lastMessageTimestamp', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            // Calculate unread count for current user
            final unreadCounts = data['unreadCounts'] as Map<String, dynamic>? ?? {};
            final unreadCount = unreadCounts[currentUser.uid] as int? ?? 0;
            // Add unreadCount to the document data
            return doc..data().addAll({'unreadCount': unreadCount});
          }).toList();
        });
  }

  // Get all users data at once
  Future<Map<String, Map<String, dynamic>>> getAllUsersData() async {
    if (_userCache.isNotEmpty) {
      return _userCache;
    }

    final usersSnapshot = await _firestore.collection('users').get();
    _userCache = {
      for (var doc in usersSnapshot.docs)
        doc.id: doc.data()
    };
    return _userCache;
  }

  // Get user data from cache or fetch if not available
  Future<Map<String, dynamic>> getUserData(String userId) async {
    if (_userCache.containsKey(userId)) {
      return _userCache[userId]!;
    }

    final userDoc = await _firestore.collection('users').doc(userId).get();
    if (userDoc.exists) {
      _userCache[userId] = userDoc.data()!;
      return userDoc.data()!;
    }
    return {};
  }

  // Clear cache when needed (e.g., on logout)
  void clearUserCache() {
    _userCache.clear();
  }

  // Get all admins
  Stream<QuerySnapshot<Map<String, dynamic>>> getAdmins() {
    return _firestore
        .collection('users')
        .where('role', isEqualTo: 'Admin')
        .snapshots();
  }

  // Get all users (parents)
  Stream<QuerySnapshot<Map<String, dynamic>>> getParents() {
    return _firestore
        .collection('users')
        .where('role', isEqualTo: 'User')
        .snapshots();
  }

  // Get conversations filtered by user type
  Stream<List<DocumentSnapshot<Map<String, dynamic>>>> getConversationsByUserType(String userType) {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final targetRole = userType == 'admin' ? 'Admin' : 'User';
    final currentUserId = currentUser.uid;

    // Query top-level /chats where current user is a participant
    return _firestore
        .collection('chats')
        .where('participants', arrayContains: currentUserId)
        .orderBy('lastMessageTimestamp', descending: true) // Order by timestamp
        .snapshots()
        .asyncMap((chatsSnapshot) async {
          final List<DocumentSnapshot<Map<String, dynamic>>> filteredChats = [];

          for (final chatDoc in chatsSnapshot.docs) {
            final participants = chatDoc.data()['participants'] as List<dynamic>?;
            if (participants == null || participants.length != 2) continue; // Should have exactly two participants

            // Find the other participant's ID
            final otherUserId = participants.firstWhere(
                (uid) => uid != currentUserId,orElse: () => null, // Handle case where participant list might be malformed
            );

            if (otherUserId == null) continue; // Skip if other user ID not found

            // Fetch the other user's data to check their role
            final otherUserDoc = await _firestore.collection('users').doc(otherUserId).get();

            if (otherUserDoc.exists) {
              final otherUserData = otherUserDoc.data();
              final otherUserRole = otherUserData?['role'] ?? 'User'; // Default to User if role is missing

              // If the other user's role matches the target role, include the chat
              if (otherUserRole == targetRole) {
                filteredChats.add(chatDoc);
              }
            }
          }

          return filteredChats; // Return the list of chats filtered by the other participant's role
        });
  }

  // Start a new chat with a user
  Future<void> startNewChat(String otherUserId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final chatId = _getChatId(currentUser.uid, otherUserId);
    final otherUserData = await getUserData(otherUserId);
    final otherUserMap = otherUserData as Map<String, dynamic>;
    final otherUserName = otherUserMap['name'] ?? otherUserMap['fullName'] ?? 'Unknown User';

    // Create/Update chat document in the top-level chats collection
    await _firestore
        .collection('chats')
        .doc(chatId)
        .set({
          'chatId': chatId,
          'participants': [currentUser.uid, otherUserId],
          'lastMessage': '',
          'lastMessageTimestamp': FieldValue.serverTimestamp(),
          // You might want to store participant names or a flag for admin/user chat here
          // e.g., 'participantNames': { currentUser.uid: currentUser.displayName ?? 'User', otherUserId: otherUserName }
          // e.g., 'isAdminChat': otherUserMap['role'] == 'Admin',
        }, SetOptions(merge: true));

    // Removed the code that created chat documents in /users/{userId}/chats
  }

  // Get all users stream (for search)
  Stream<QuerySnapshot<Map<String, dynamic>>> getAllUsersStream() {
    return _firestore.collection('users').snapshots();
  }

  // Search users by name
  Stream<List<DocumentSnapshot<Map<String, dynamic>>>> searchUsersByName(String query) {
    if (query.isEmpty) {
      return getAllUsersStream().map((snapshot) => snapshot.docs);
    }

    // Convert query to lowercase for case-insensitive search
    final searchQuery = query.toLowerCase();
    
    return getAllUsersStream().map((snapshot) {
      return snapshot.docs.where((doc) {
        final data = doc.data();
        final name = (data['name'] ?? '').toString().toLowerCase();
        return name.contains(searchQuery);
      }).toList();
    });
  }

  // Mark messages as read when opening chat
  Future<void> markMessagesAsRead(String chatId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    // Reset unread count for current user
    await _firestore.collection('chats').doc(chatId).update({
      'unreadCounts.${currentUser.uid}': 0,
    });
  }
}