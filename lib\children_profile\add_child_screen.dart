import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:gymgo/Service/child_profile_service.dart';

class AddChildScreen extends StatefulWidget {
  const AddChildScreen({super.key});

  @override
  _AddChildScreenState createState() => _AddChildScreenState();
}

class _AddChildScreenState extends State<AddChildScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ChildProfileService _childProfileService = ChildProfileService();

  final List<String> _avatars = [
    'assets/children_avatar/bear.png',
    'assets/children_avatar/chicken.png',
    'assets/children_avatar/dog.png',
    'assets/children_avatar/duck.png',
    'assets/children_avatar/meerkat.png',
    'assets/children_avatar/panda.png',
    'assets/children_avatar/rabbit.png',
    'assets/children_avatar/shark.png',
    'assets/children_avatar/elephant.png',
    'assets/children_avatar/tiger.png',
    'assets/children_avatar/beaver.png',
  ];

  final fullNameController = TextEditingController();
  DateTime? selectedDate;
  String selectedGender = 'Male';
  String? selectedAvatarPath;
  bool isFullNameEmpty = true;

  @override
  void initState() {
    super.initState();
    fullNameController.addListener(() {
      setState(() {
        isFullNameEmpty = fullNameController.text.isEmpty;
      });
    });
  }

  @override
  void dispose() {
    fullNameController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF8B5E3C), // Your theme's brown color
              onPrimary: Colors.white, // Color of text/icons on primary
              surface: Color(0xFFF9EBD7), // Your theme's light beige color
              onSurface: Colors.black87, // Color of text/icons on surface
            ),
            dialogTheme: DialogTheme(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0), // Apply rounded corners
              ),
            ),
          ),
          child: child!,
        );
      },
      initialDate: selectedDate ?? DateTime.now(), // Use selectedDate if available
      firstDate: DateTime(1900), // Adjusted first date to allow older birth dates
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  Future<void> _addChild() async {
    if (fullNameController.text.isNotEmpty && selectedDate != null) {
      try {
        await _childProfileService.createChildProfile(
          fullName: fullNameController.text,
          birthDate: selectedDate!,
          gender: selectedGender,
          avatarPath: selectedAvatarPath,
        );

        if (mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding child: $e')),
        );
      }
    }
  }

  Widget _buildTextField(
    String label,
    IconData icon,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      style: TextStyle(
        color: isFullNameEmpty ? Colors.black54 : Colors.black,
      ),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: Colors.black54),
        prefixIcon: Icon(icon, color: const Color(0xFF8B5E3C)),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF8B5E3C), width: 2),
        ),
      ),
    );
  }

  Widget _buildSectionCard({required String title, required List<Widget> children}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Color(0xFF8B5E3C))),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Add Child Profile',
          style: TextStyle(
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionCard(
              title: "Select Avatar",
              children: [
                Container(
                  height: 120,
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 6,
                      crossAxisSpacing: 6.0,
                      mainAxisSpacing: 6.0,
                    ),
                    itemCount: _avatars.length,
                    itemBuilder: (context, index) {
                      final avatarPath = _avatars[index];
                      final isSelected = selectedAvatarPath == avatarPath;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedAvatarPath = avatarPath;
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFF8B5E3C)
                                  : Colors.transparent,
                              width: 3,
                            ),
                          ),
                          child: CircleAvatar(
                            radius: 30,
                            backgroundColor: const Color(0xFFF9EBD7),
                            backgroundImage: AssetImage(avatarPath),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ]
            ),

            const SizedBox(height: 30),

            _buildSectionCard(
              title: "Profile Information",
              children: [
                _buildTextField('Full Name', Icons.person, fullNameController),
                const SizedBox(height: 16),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xFF8B5E3C)),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    leading: const Icon(Icons.calendar_today, color: Color(0xFF8B5E3C)),
                    title: Text(
                      selectedDate == null
                          ? 'Select Date of Birth'
                          : DateFormat('MM/dd/yyyy').format(selectedDate!),
                      style: TextStyle(
                        color: selectedDate == null ? Colors.black54 : Colors.black,
                      ),
                    ),
                    onTap: () => _selectDate(context),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: null,
                  hint: const Text('Select Gender'),
                  decoration: InputDecoration(
                    labelText: 'Gender',
                    prefixIcon: Icon(Icons.people, color: const Color(0xFF8B5E3C)),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C), width: 2),
                    ),
                  ),
                  items: ['Male', 'Female']
                      .map((gender) => DropdownMenuItem(
                            value: gender,
                            child: Text(
                              gender,
                              style: const TextStyle(color: Colors.black),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedGender = value ?? '';
                    });
                  },
                ),
              ]
            ),

            const SizedBox(height: 30),

            Center(
              child: ElevatedButton(
                onPressed: _addChild,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF8B5E3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 3,
                ),
                child: const Text('Add Child'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 