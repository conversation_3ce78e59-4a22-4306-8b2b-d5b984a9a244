import 'package:flutter/material.dart';

class ProfileMenuTile extends StatefulWidget {
  const ProfileMenuTile({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  @override
  State<ProfileMenuTile> createState() => _ProfileMenuTileState();
}

class _ProfileMenuTileState extends State<ProfileMenuTile> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _setHovering(true),
      onExit: (_) => _setHovering(false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          decoration: BoxDecoration(
            color: _isHovering ? Colors.brown.withOpacity(0.1) : Colors.transparent, // Subtle hover effect
            borderRadius: BorderRadius.circular(8.0), // Optional: add some border radius on hover
          ),
          child: ListTile(
            leading: Icon(widget.icon, color: const Color(0xFF8B5E3C)),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            title: Text(widget.title, style: const TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Text(widget.subtitle),
            trailing: const Icon(Icons.chevron_right), // Add trailing icon back
          ),
        ),
      ),
    );
  }

  void _setHovering(bool isHovering) {
    if (_isHovering != isHovering) {
      setState(() {
        _isHovering = isHovering;
      });
    }
  }
} 