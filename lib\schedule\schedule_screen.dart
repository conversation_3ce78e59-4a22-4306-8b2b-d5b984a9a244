import 'package:flutter/material.dart';
import 'add_schedule.dart';
import 'edit_schedule.dart';
import 'gym_class.dart';
import '../service/schedule_service.dart';

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  final ScheduleService _scheduleService = ScheduleService();
  List<GymClass> _classes = [];
  List<GymClass> _filteredClasses = [];
  bool _isLoading = true;
  String? _error;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadClasses();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadClasses() {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _scheduleService.getClasses().listen(
        (classes) {
          setState(() {
            _classes = classes;
            _classes.sort((a, b) => a.date.compareTo(b.date));
            _filteredClasses = _classes;
            _isLoading = false;
          });
        },
        onError: (error) {
          setState(() {
            _error = error.toString();
            _isLoading = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterClasses(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredClasses = _classes;
      } else {
        _filteredClasses = _classes.where((class_) {
          return class_.name.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _showDeleteConfirmation(GymClass gymClass) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.warning_rounded,
                  color: Color.fromARGB(255, 211, 169, 137),
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Confirm Delete',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 70, 70, 70),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Are you sure you want to delete "${gymClass.name}" class?',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color.fromARGB(255, 109, 109, 109),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color.fromARGB(255, 109, 109, 109),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text('Delete'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).then((confirmed) async {
      if (confirmed == true && gymClass.id != null) {
        try {
          await _scheduleService.deleteClass(gymClass.id!);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Class deleted successfully!'),
                backgroundColor: Color.fromARGB(255, 205, 148, 104),
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error deleting class: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Class Schedule',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Color.fromARGB(255, 80, 80, 80),
          ),
        ),
        backgroundColor: const Color.fromARGB(255, 211, 169, 137),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 211, 169, 137),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search class name...',
                hintStyle: const TextStyle(color: Colors.white70),
                prefixIcon: const Icon(Icons.search, color: Colors.white),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: const Color.fromARGB(255, 205, 148, 104),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: _filterClasses,
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Error: $_error',
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadClasses,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color.fromARGB(255, 211, 169, 137),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredClasses.isEmpty
                        ? const Center(
                            child: Text(
                              'No classes found.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 18,
                                color: Color.fromARGB(255, 109, 109, 109),
                              ),
                            ),
                          )
                        : RefreshIndicator(
                            onRefresh: () async {
                              _loadClasses();
                            },
                            child: ListView(
                              padding: const EdgeInsets.all(16),
                              children: [
                                // Upcoming Classes Section
                                const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: Text(
                                    'Upcoming Classes',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Color.fromARGB(255, 109, 109, 109),
                                    ),
                                  ),
                                ),
                                ..._filteredClasses
                                    .where((class_) => !class_.date.isBefore(DateTime.now()))
                                    .map((gymClass) => Card(
                                          margin: const EdgeInsets.only(bottom: 16),
                                          elevation: 4,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              gradient: LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: [
                                                  const Color.fromARGB(255, 255, 255, 255),
                                                  const Color.fromARGB(255, 239, 232, 222),
                                                ],
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(16),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Expanded(
                                                        child: Text(
                                                          gymClass.name,
                                                          style: const TextStyle(
                                                            fontSize: 20,
                                                            fontWeight: FontWeight.bold,
                                                            color: Color.fromARGB(255, 70, 70, 70),
                                                          ),
                                                        ),
                                                      ),
                                                      Row(
                                                        mainAxisSize: MainAxisSize.min,
                                                        children: [
                                                          IconButton(
                                                            icon: const Icon(
                                                              Icons.edit,
                                                              color: Color.fromARGB(255, 211, 169, 137),
                                                            ),
                                                            onPressed: () => EditScheduleDialog.show(
                                                              context,
                                                              _scheduleService,
                                                              gymClass,
                                                            ),
                                                            tooltip: 'Edit class',
                                                          ),
                                                          IconButton(
                                                            icon: const Icon(
                                                              Icons.delete,
                                                              color: Colors.red,
                                                            ),
                                                            onPressed: () => _showDeleteConfirmation(gymClass),
                                                            tooltip: 'Delete class',
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 12),
                                                  _buildInfoRow(Icons.person, 'Instructor', gymClass.instructor),
                                                  _buildInfoRow(Icons.calendar_today, 'Date', '${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year}'),
                                                  _buildInfoRow(Icons.access_time, 'Time', gymClass.time),
                                                  _buildInfoRow(Icons.timer, 'Duration', gymClass.duration),
                                                  _buildInfoRow(
                                                    Icons.attach_money,
                                                    'Payment',
                                                    (gymClass.payment == null || gymClass.payment == 0)
                                                        ? 'Free'
                                                        : gymClass.payment!.toStringAsFixed(2),
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      _buildCapacityInfo('Capacity', gymClass.capacity),
                                                      _buildCapacityInfo('Enrolled', gymClass.enrolled),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ))
                                    .toList(),
                                const SizedBox(height: 24),
                                // Past Classes Section
                                const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: Text(
                                    'Past Classes',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Color.fromARGB(255, 109, 109, 109),
                                    ),
                                  ),
                                ),
                                ..._filteredClasses
                                    .where((class_) => class_.date.isBefore(DateTime.now()))
                                    .map((gymClass) => Card(
                                          margin: const EdgeInsets.only(bottom: 16),
                                          elevation: 4,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              gradient: LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: [
                                                  const Color.fromARGB(255, 255, 255, 255),
                                                  const Color.fromARGB(255, 239, 232, 222),
                                                ],
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(16),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Expanded(
                                                        child: Text(
                                                          gymClass.name,
                                                          style: const TextStyle(
                                                            fontSize: 20,
                                                            fontWeight: FontWeight.bold,
                                                            color: Color.fromARGB(255, 70, 70, 70),
                                                          ),
                                                        ),
                                                      ),
                                                      // No edit/delete buttons for past classes
                                                    ],
                                                  ),
                                                  const SizedBox(height: 12),
                                                  _buildInfoRow(Icons.person, 'Instructor', gymClass.instructor),
                                                  _buildInfoRow(Icons.calendar_today, 'Date', '${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year}'),
                                                  _buildInfoRow(Icons.access_time, 'Time', gymClass.time),
                                                  _buildInfoRow(Icons.timer, 'Duration', gymClass.duration),
                                                  _buildInfoRow(
                                                    Icons.attach_money,
                                                    'Payment',
                                                    (gymClass.payment == null || gymClass.payment == 0)
                                                        ? 'Free'
                                                        : gymClass.payment!.toStringAsFixed(2),
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      _buildCapacityInfo('Capacity', gymClass.capacity),
                                                      _buildCapacityInfo('Enrolled', gymClass.enrolled),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ))
                                    .toList(),
                              ],
                            ),
                          ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => AddScheduleDialog.show(context, _scheduleService),
        backgroundColor: const Color.fromARGB(255, 211, 169, 137),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 18, color: const Color.fromARGB(255, 109, 109, 109)),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color.fromARGB(255, 109, 109, 109),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Color.fromARGB(255, 70, 70, 70),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCapacityInfo(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 211, 169, 137).withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color.fromARGB(255, 109, 109, 109),
            ),
          ),
          Text(
            value.toString(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Color.fromARGB(255, 70, 70, 70),
            ),
          ),
        ],
      ),
    );
  }
}