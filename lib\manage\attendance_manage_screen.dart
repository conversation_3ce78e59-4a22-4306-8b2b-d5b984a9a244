import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'attendance_add_screen.dart';
import 'attendance_edit_screen.dart';

class AttendanceManageScreen extends StatefulWidget {
  final String classId;
  final String className;
  final DateTime classDate;

  const AttendanceManageScreen({
    super.key,
    required this.classId,
    required this.className,
    required this.classDate,
  });

  @override
  _AttendanceManageScreenState createState() => _AttendanceManageScreenState();
}

class _AttendanceManageScreenState extends State<AttendanceManageScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by user name or class name',
              prefixIcon: const Icon(Icons.search, color: Color(0xFF8B5E3C)),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
              ),
            ),
            onChanged: (value) {
              setState(() {});
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceCard(Map<String, dynamic> attendance) {
    // Safely access Timestamp fields with null checks
    final Timestamp? checkInTime = attendance['checkInTime'] as Timestamp?;
    final Timestamp? createdAt = attendance['createdAt'] as Timestamp?;
    // Assuming updatedAt might also be a Timestamp and could be null
    final Timestamp? updatedAt = attendance['updatedAt'] as Timestamp?;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  attendance['className'] ?? 'Unknown Class',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF8B5E3C),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(attendance['status']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    attendance['status'] ?? 'Unknown',
                    style: TextStyle(
                      color: _getStatusColor(attendance['status']),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, color: Color(0xFF8B5E3C), size: 16),
                const SizedBox(width: 8),
                Text(
                  attendance['userName'] ?? 'Unknown user',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.access_time, color: Color(0xFF8B5E3C), size: 16),
                const SizedBox(width: 8),
                Text(
                  // Use the safely accessed checkInTime
                  checkInTime != null
                      ? DateFormat('hh:mm a').format(checkInTime.toDate())
                      : 'Not checked in',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
             // Display createdAt and updatedAt if available (optional, for debugging/info)
             if (createdAt != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.add_circle_outline, color: Colors.grey, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Created: ${DateFormat('yyyy-MM-dd HH:mm').format(createdAt.toDate())}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
            if (updatedAt != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.update, color: Colors.grey, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Updated: ${DateFormat('yyyy-MM-dd HH:mm').format(updatedAt.toDate())}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AttendanceEditScreen(
                          attendanceData: attendance,
                          attendanceId: attendance['id'],
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.edit, color: Color(0xFF8B5E3C)),
                  label: const Text('Edit'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showDeleteConfirmation(attendance['id']),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  label: const Text('Delete'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'present':
        return Colors.green;
      case 'absent':
        return Colors.red;
      case 'late':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Future<void> _showDeleteConfirmation(String attendanceId) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Attendance'),
        content: const Text('Are you sure you want to delete this attendance record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _firestore
                    .collection('attendance')
                    .doc(attendanceId)
                    .delete();
                if (mounted) {
                  Navigator.of(context, rootNavigator: true).pop();
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    const SnackBar(
                      content: Text('Attendance record deleted successfully'),
                      backgroundColor: Color.fromARGB(255, 205, 148, 104),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(content: Text('Error deleting attendance: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: Text(
          '${widget.className} - ${DateFormat('MM/dd/yyyy').format(widget.classDate)} Attendance',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          _buildFilterSection(),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: _firestore
                  .collection('attendance')
                  .where('classId', isEqualTo: widget.classId)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Center(
                    child: Text('Error: ${snapshot.error}'),
                  );
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5E3C)),
                    ),
                  );
                }

                final attendances = snapshot.data?.docs ?? [];

                final filteredAttendances = attendances.where((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  final className = (data['className'] ?? '').toString().toLowerCase();
                  final userName = (data['userName'] ?? '').toString().toLowerCase();
                  final searchText = _searchController.text.toLowerCase();

                  // Safely access and check the 'date' field
                  final dynamic attendanceDateData = data['date'];
                  DateTime? attendanceDateNormalized;

                  if (attendanceDateData is Timestamp) {
                    final attendanceDate = attendanceDateData.toDate();
                     attendanceDateNormalized = DateTime(attendanceDate.year, attendanceDate.month, attendanceDate.day);
                  } else {
                     // If date is not a Timestamp or is null, treat it as not matching the selected date
                     return false; 
                  }

                  final selectedDateNormalized = DateTime(widget.classDate.year, widget.classDate.month, widget.classDate.day);

                  final dateMatches = attendanceDateNormalized.isAtSameMomentAs(selectedDateNormalized);

                  final searchMatches = className.contains(searchText) || userName.contains(searchText);

                  return dateMatches && searchMatches; // Only include documents where the date matches and search criteria are met
                }).toList();

                if (filteredAttendances.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.event_busy,
                          size: 100,
                          color: Color(0xFF8B5E3C),
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          'No attendance records found',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Add new attendance records to get started',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                        const SizedBox(height: 32),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AttendanceAddScreen(
                                  classId: widget.classId,
                                  className: widget.className,
                                  classDate: widget.classDate,
                                ),
                              ),
                            );
                          },
                          icon: const Icon(Icons.add_circle_outline),
                          label: const Text('Add Attendance'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF8B5E3C),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                            textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                            elevation: 4,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: filteredAttendances.length,
                  itemBuilder: (context, index) {
                    final attendance = filteredAttendances[index].data() as Map<String, dynamic>;
                    attendance['id'] = filteredAttendances[index].id;
                    // Pass the data to _buildAttendanceCard, which now has null checks
                    return _buildAttendanceCard(attendance);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AttendanceAddScreen(
                classId: widget.classId,
                className: widget.className,
                classDate: widget.classDate,
              ),
            ),
          );
        },
        backgroundColor: const Color(0xFF8B5E3C),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_circle_outline),
        label: const Text('Add Attendance'),
      ),
    );
  }
} 