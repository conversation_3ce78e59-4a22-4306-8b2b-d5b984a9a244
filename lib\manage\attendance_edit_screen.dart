import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';

class AttendanceEditScreen extends StatefulWidget {
  final Map<String, dynamic> attendanceData;
  final String attendanceId;

  const AttendanceEditScreen({
    super.key,
    required this.attendanceData,
    required this.attendanceId,
  });

  @override
  _AttendanceEditScreenState createState() => _AttendanceEditScreenState();
}

class _AttendanceEditScreenState extends State<AttendanceEditScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  late String _selectedStatus;
  late DateTime _selectedDate;
  late TimeOfDay _selectedTime;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.attendanceData['status'] ?? 'Present';
    _selectedDate = (widget.attendanceData['date'] as Timestamp).toDate();
    _selectedTime = TimeOfDay.fromDateTime(
      (widget.attendanceData['checkInTime'] as Timestamp).toDate(),
    );
    _notesController.text = widget.attendanceData['notes'] ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color.fromARGB(255, 211, 169, 137),
              onPrimary: Colors.white,
              surface: Color.fromARGB(255, 239, 232, 222),
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
      helpText: '',
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<TimeOfDay?> _showCustomTimePicker(BuildContext context, TimeOfDay initialTime) async {
    int hour = initialTime.hourOfPeriod;
    int minute = initialTime.minute;
    bool isPM = initialTime.period == DayPeriod.pm;
    final hourController = TextEditingController(text: hour.toString().padLeft(2, '0'));
    final minuteController = TextEditingController(text: minute.toString().padLeft(2, '0'));
    bool confirmed = false;
    String? errorText;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
              title: const Text('Enter Time', style: TextStyle(fontWeight: FontWeight.bold)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 70,
                        child: TextField(
                          controller: hourController,
                          keyboardType: TextInputType.number,
                          maxLength: 2,
                          decoration: InputDecoration(
                            labelText: 'Hour',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                            counterText: '',
                          ),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text(':', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                      ),
                      SizedBox(
                        width: 70,
                        child: TextField(
                          controller: minuteController,
                          keyboardType: TextInputType.number,
                          maxLength: 2,
                          decoration: InputDecoration(
                            labelText: 'Minute',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                            counterText: '',
                          ),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ToggleButtons(
                    isSelected: [!isPM, isPM],
                    borderRadius: BorderRadius.circular(8),
                    selectedColor: Colors.white,
                    fillColor: const Color(0xFF8B5E3C),
                    color: const Color(0xFF8B5E3C),
                    children: const [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
                        child: Text('AM', style: TextStyle(fontSize: 16)),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
                        child: Text('PM', style: TextStyle(fontSize: 16)),
                      ),
                    ],
                    onPressed: (index) {
                      setState(() {
                        isPM = index == 1;
                      });
                    },
                  ),
                  if (errorText != null) ...[
                    const SizedBox(height: 12),
                    Text(errorText!, style: const TextStyle(color: Colors.red, fontSize: 13)),
                  ],
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF8B5E3C),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  onPressed: () {
                    int? enteredHour = int.tryParse(hourController.text);
                    int? enteredMinute = int.tryParse(minuteController.text);
                    if (enteredHour == null || enteredMinute == null || enteredHour < 1 || enteredHour > 12 || enteredMinute < 0 || enteredMinute > 59) {
                      setState(() {
                        errorText = 'Please enter a valid time (1-12 for hour, 0-59 for minute).';
                      });
                      return;
                    }
                    confirmed = true;
                    Navigator.of(context).pop();
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );

    if (!confirmed) return null;
    int? enteredHour = int.tryParse(hourController.text);
    int? enteredMinute = int.tryParse(minuteController.text);
    if (enteredHour == null || enteredMinute == null || enteredHour < 1 || enteredHour > 12 || enteredMinute < 0 || enteredMinute > 59) {
      return null;
    }
    int finalHour = enteredHour % 12 + (isPM ? 12 : 0);
    return TimeOfDay(hour: finalHour, minute: enteredMinute);
  }

  Future<void> _updateAttendance() async {
    try {
      final checkInDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      await _firestore.collection('attendance').doc(widget.attendanceId).update({
        'status': _selectedStatus,
        'date': Timestamp.fromDate(_selectedDate),
        'checkInTime': Timestamp.fromDate(checkInDateTime),
        'notes': _notesController.text,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': _auth.currentUser?.uid,
      });

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Attendance record updated successfully'),
            backgroundColor: Color.fromARGB(255, 205, 148, 104),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating attendance: $e')),
      );
    }
  }

  Widget _buildSectionCard({required String title, required List<Widget> children}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Edit Attendance',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionCard(
              title: 'Class Information',
              children: [
                Text(
                  'Class: ${widget.attendanceData['className'] ?? 'Unknown Class'}',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(
                  'User: ${widget.attendanceData['userName'] ?? 'Unknown User'}',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSectionCard(
              title: 'Attendance Details',
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                  items: ['Present', 'Absent', 'Late']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                  isDense: true,
                  isExpanded: true,
                ),
                const SizedBox(height: 16),
                if (_selectedStatus.toLowerCase() != 'absent')
                  TextFormField(
                    readOnly: true,
                    controller: TextEditingController(text: _selectedTime.format(context)),
                    decoration: InputDecoration(
                      labelText: 'Check-in Time',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                      ),
                      prefixIcon: const Icon(Icons.access_time, color: Color(0xFF8B5E3C)),
                    ),
                    onTap: () async {
                      final picked = await _showCustomTimePicker(context, _selectedTime);
                      if (picked != null) {
                        setState(() {
                          _selectedTime = picked;
                        });
                      }
                    },
                  ),
                if (_selectedStatus.toLowerCase() != 'absent') const SizedBox(height: 16),
                TextField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 30),
            Center(
              child: ElevatedButton(
                onPressed: _updateAttendance,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF8B5E3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 3,
                ),
                child: const Text('Save Changes'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 