import 'package:flutter/material.dart';

/// A reusable widget to display a child's avatar image or fallback to the first letter of the name.
/// [avatarPath]: asset path for the avatar image. If null or empty, fallback to initial.
/// [displayName]: the name to use for the fallback initial.
/// [radius]: the radius of the CircleAvatar.
/// [imageSize]: the width/height of the image inside the avatar.
Widget buildChildAvatar({
  required String? avatarPath,
  required String displayName,
  double radius = 30,
  double imageSize = 60,
}) {
  return CircleAvatar(
    radius: radius,
    backgroundColor: const Color(0xFFF9EBD7),
    child: avatarPath != null && avatarPath.isNotEmpty
        ? Image.asset(
            avatarPath,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Text(
                displayName.isNotEmpty ? displayName[0].toUpperCase() : '?',
                style: const TextStyle(
                  fontSize: 24,
                  color: Color(0xFF8B5E3C),
                ),
              );
            },
          )
        : Text(
            displayName.isNotEmpty ? displayName[0].toUpperCase() : '?',
            style: const TextStyle(
              fontSize: 24,
              color: Color(0xFF8B5E3C),
            ),
          ),
  );
} 