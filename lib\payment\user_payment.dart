import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gymgo/Service/stripe_service.dart';

class UserPaymentScreen extends StatefulWidget {
  const UserPaymentScreen({super.key});

  @override
  _UserPaymentScreenState createState() => _UserPaymentScreenState();
}

class _UserPaymentScreenState extends State<UserPaymentScreen>
    with SingleTickerProviderStateMixin {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<List<Map<String, dynamic>>> _fetchChildrenWithBookings() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return [];

    // Query children collection where userId matches current user
    final childrenSnapshot =
        await _firestore
            .collection('children')
            .where('userId', isEqualTo: userId)
            .get();

    List<Map<String, dynamic>> childrenData = [];

    for (var doc in childrenSnapshot.docs) {
      final data = doc.data();
      final bookedClasses = data['bookedClasses'] ?? [];
      childrenData.add({
        'childId': doc.id,
        'fullName': data['fullName'] ?? 'No Name',
        'bookedClasses': bookedClasses,
      });
    }

    return childrenData;
  }

  Future<void> _processPaymentAndUpdate(
    String childId,
    int index,
    double paymentAmount,
  ) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    final success = await StripeService.instance.makePayment(paymentAmount);

    if (success) {
      final childRef = _firestore.collection('children').doc(childId);

      final childDoc = await childRef.get();
      List<dynamic> bookedClasses = childDoc.data()?['bookedClasses'] ?? [];

      if (index >= 0 && index < bookedClasses.length) {
        bookedClasses[index]['status'] = 'Paid';

        await childRef.update({'bookedClasses': bookedClasses});
        setState(() {});
      }

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Payment successful!')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Payment failed or canceled.')),
      );
    }
  }

  void _showPaymentDialog(String childId, int index, double paymentAmount) {
    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Confirm Payment'),
            content: Text(
              'Pay RM${paymentAmount.toStringAsFixed(2)} for this class?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(ctx);
                  await _processPaymentAndUpdate(childId, index, paymentAmount);
                },
                child: const Text('Pay Now'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Class Payment',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _fetchChildrenWithBookings(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text("No children or bookings found."));
          }

          final children = snapshot.data!;

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: children.length,
            itemBuilder: (context, index) {
              final child = children[index];
              final fullName = child['fullName'];
              final childId = child['childId'];
              final bookedClasses = child['bookedClasses'] as List<dynamic>;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fullName,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...bookedClasses.asMap().entries.map((entry) {
                    final booking = entry.value;
                    final i = entry.key;
                    final status = booking['status'] ?? 'Unknown';
                    final isPending = status == 'Pending';

                    return Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        booking['className'] ?? 'Unknown Class',
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        "Payment: RM${booking['payment'] ?? 0}",
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        (status == "Paid")
                                            ? Colors.green[100]
                                            : Colors.red[100],
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    status,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          (status == "Paid")
                                              ? Colors.green[800]
                                              : Colors.red[800],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (isPending)
                              Align(
                                alignment: Alignment.centerRight,
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: ElevatedButton(
                                    onPressed:
                                        () => _showPaymentDialog(
                                          childId,
                                          i,
                                          double.tryParse(
                                                "${booking['payment']}",
                                              ) ??
                                              0.0,
                                        ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color.fromARGB(
                                        255,
                                        211,
                                        169,
                                        137,
                                      ),
                                      foregroundColor: Colors.white,
                                    ),
                                    child: const Text('Pay Now'),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                  const SizedBox(height: 16),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
