import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'gym_class.dart';
import '../service/schedule_service.dart';

class AddScheduleDialog extends StatefulWidget {
  final Function(GymClass) onAddClass;

  const AddScheduleDialog({
    super.key,
    required this.onAddClass,
  });

  static void show(BuildContext context, ScheduleService scheduleService) {
    showDialog(
      context: context,
      builder: (context) => AddScheduleDialog(
        onAddClass: (GymClass newClass) async {
          try {
            // Check for existing classes at the same date and time
            final existingClasses = await FirebaseFirestore.instance
                .collection('gym_classes')
                .where('date', isEqualTo: newClass.date.toIso8601String())
                .where('time', isEqualTo: newClass.time)
                .get();

            if (existingClasses.docs.isNotEmpty) {
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('A class already exists at this date and time!'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
              return;
            }

            await scheduleService.addClass(newClass);
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Class added successfully!'),
                  backgroundColor: Color.fromARGB(255, 205, 148, 104),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error adding class: ${e.toString()}'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        },
      ),
    );
  }

  @override
  State<AddScheduleDialog> createState() => _AddScheduleDialogState();
}

class _AddScheduleDialogState extends State<AddScheduleDialog> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController paymentController = TextEditingController();
  final TextEditingController capacityController = TextEditingController();
  String selectedTime = 'Select time';
  String selectedDuration = 'Select duration';
  DateTime? selectedDate;
  String? dateError;
  String? _adminName;

  // Add error message states
  String? nameError;
  String? timeError;
  String? durationError;
  String? capacityError;
  String? paymentError;

  final List<String> timeOptions = [
    'Select time',
    '08:00 AM', '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM', '06:00 PM',
    '07:00 PM', '08:00 PM'
  ];

  final List<String> durationOptions = [
    'Select duration',
    '30 min', '40 min', '50 min', '60 min', '90 min'
  ];

  @override
  void initState() {
    super.initState();
    _loadAdminName();
  }

  Future<void> _loadAdminName() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();
      if (mounted) {
        setState(() {
          _adminName = doc['name'] as String?;
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color.fromARGB(255, 211, 169, 137),
              onPrimary: Colors.white,
              surface: Color.fromARGB(255, 239, 232, 222),
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
      helpText: '',
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        dateError = null;
        // Reset time if it's in the past for today's date
        if (picked.year == DateTime.now().year && 
            picked.month == DateTime.now().month && 
            picked.day == DateTime.now().day &&
            selectedTime != 'Select time') {
          // Get current time in Malaysia (UTC+8)
          final now = DateTime.now().toUtc().add(const Duration(hours: 8));
          final currentHour = now.hour;
          final currentMinute = now.minute;
          
          // Convert selected time to 24-hour format for comparison
          final timeParts = selectedTime.split(' ');
          final time = timeParts[0].split(':');
          final hour = int.parse(time[0]);
          final isPM = timeParts[1] == 'PM';
          final selectedHour = isPM && hour != 12 ? hour + 12 : hour;
          
          if (selectedHour < currentHour || 
              (selectedHour == currentHour && currentMinute > 0)) {
            selectedTime = 'Select time';
            timeError = 'Please select a future time for today';
          }
        }
      });
    }
  }

  void _updateTime(String? newValue) {
    if (newValue != null) {
      if (selectedDate != null && 
          selectedDate!.year == DateTime.now().year && 
          selectedDate!.month == DateTime.now().month && 
          selectedDate!.day == DateTime.now().day &&
          newValue != 'Select time') {
        // Get current time in Malaysia (UTC+8)
        final now = DateTime.now().toUtc().add(const Duration(hours: 8));
        final currentHour = now.hour;
        final currentMinute = now.minute;
        
        // Convert selected time to 24-hour format for comparison
        final timeParts = newValue.split(' ');
        final time = timeParts[0].split(':');
        final hour = int.parse(time[0]);
        final isPM = timeParts[1] == 'PM';
        final selectedHour = isPM && hour != 12 ? hour + 12 : hour;
        
        if (selectedHour < currentHour || 
            (selectedHour == currentHour && currentMinute > 0)) {
          setState(() {
            timeError = 'Please select a future time for today';
          });
          return;
        }
      }
      
      setState(() {
        selectedTime = newValue;
        timeError = null;
      });
    }
  }

  void _addClass() {
    setState(() {
      nameError = null;
      timeError = null;
      durationError = null;
      capacityError = null;
      dateError = null;
      paymentError = null;
    });

    bool isValid = true;

    if (nameController.text.isEmpty) {
      setState(() {
        nameError = 'Please enter a class name';
      });
      isValid = false;
    }
    if (selectedTime == 'Select time') {
      setState(() {
        timeError = 'Please select a time';
      });
      isValid = false;
    }
    if (selectedDuration == 'Select duration') {
      setState(() {
        durationError = 'Please select a duration';
      });
      isValid = false;
    }
    if (capacityController.text.isEmpty) {
      setState(() {
        capacityError = 'Please enter capacity';
      });
      isValid = false;
    } else {
      final capacity = int.tryParse(capacityController.text);
      if (capacity == null || capacity <= 0) {
        setState(() {
          capacityError = 'Please enter a valid positive number';
        });
        isValid = false;
      }
    }
    if (selectedDate == null) {
      setState(() {
        dateError = 'Please select a date';
      });
      isValid = false;
    }

    // Validate payment input
    final double? paymentValue = double.tryParse(paymentController.text);
    if (paymentController.text.isNotEmpty && paymentValue == null) {
      setState(() {
        paymentError = 'Please enter a valid number for payment';
      });
      isValid = false;
    }
    if (paymentController.text.isEmpty) {
      setState(() {
        paymentError = 'Please enter payment amount';
      });
      isValid = false;
    }

    if (isValid) {
      final newClass = GymClass(
        name: nameController.text,
        instructor: _adminName ?? 'Admin',
        date: selectedDate!,
        time: selectedTime,
        duration: selectedDuration,
        capacity: int.parse(capacityController.text),
        enrolled: 0,
        payment: paymentValue,
      );
      widget.onAddClass(newClass);
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    paymentController.dispose();
    capacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Add New Class',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 70, 70, 70),
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'Class Name',
                  errorText: nameError,
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 107, 107, 107)),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color.fromARGB(255, 139, 94, 60)),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[400]!),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: paymentController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Payment Amount',
                  errorText: paymentError,
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 107, 107, 107)),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color.fromARGB(255, 139, 94, 60)),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[400]!),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
                onChanged: (value) {
                   setState(() {
                       paymentError = null;
                   });
                },
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Date',
                    labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                    errorText: dateError,
                    errorStyle: const TextStyle(color: Colors.red),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                    ),
                    prefixIcon: const Icon(Icons.calendar_today, color: Color.fromARGB(255, 211, 169, 137)),
                    suffixIcon: const Icon(Icons.arrow_drop_down, color: Color.fromARGB(255, 109, 109, 109)),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  child: Text(
                    selectedDate != null
                        ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                        : 'Select date',
                    style: TextStyle(
                      color: selectedDate != null 
                          ? Colors.black 
                          : const Color.fromARGB(255, 109, 109, 109),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedTime,
                decoration: InputDecoration(
                  labelText: 'Time',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorText: timeError,
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.access_time, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
                dropdownColor: Colors.white,
                menuMaxHeight: 200,
                items: timeOptions.map((String value) {
                  // Disable past times for today
                  bool isDisabled = false;
                  if (selectedDate != null && 
                      selectedDate!.year == DateTime.now().year && 
                      selectedDate!.month == DateTime.now().month && 
                      selectedDate!.day == DateTime.now().day &&
                      value != 'Select time') {
                    // Get current time in Malaysia (UTC+8)
                    final now = DateTime.now().toUtc().add(const Duration(hours: 8));
                    final currentHour = now.hour;
                    final currentMinute = now.minute;
                    
                    final timeParts = value.split(' ');
                    final time = timeParts[0].split(':');
                    final hour = int.parse(time[0]);
                    final isPM = timeParts[1] == 'PM';
                    final selectedHour = isPM && hour != 12 ? hour + 12 : hour;
                    
                    isDisabled = selectedHour < currentHour || 
                               (selectedHour == currentHour && currentMinute > 0);
                  }
                  
                  return DropdownMenuItem<String>(
                    value: value,
                    enabled: !isDisabled,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: value == 'Select time' 
                            ? const Color.fromARGB(255, 109, 109, 109)
                            : isDisabled 
                                ? const Color.fromARGB(255, 200, 200, 200)
                                : Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: _updateTime,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedDuration,
                decoration: InputDecoration(
                  labelText: 'Duration',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorText: durationError,
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.timer, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
                dropdownColor: Colors.white,
                menuMaxHeight: 200,
                items: durationOptions.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: value == 'Select duration' 
                            ? const Color.fromARGB(255, 109, 109, 109)
                            : Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedDuration = newValue!;
                    durationError = null;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: capacityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Capacity',
                  errorText: capacityError,
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 107, 107, 107)),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color.fromARGB(255, 139, 94, 60)),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[400]!),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  prefixIcon: const Icon(Icons.people, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                onChanged: (value) {
                  setState(() {
                    capacityError = null;
                  });
                },
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: const Color.fromARGB(255, 109, 109, 109),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _addClass,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 211, 169, 137),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text('Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 