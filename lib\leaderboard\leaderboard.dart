import 'package:flutter/material.dart';
import 'package:gymgo/Service/attendance_service.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({Key? key}) : super(key: key);

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen> {
  final AttendanceService _attendanceService = AttendanceService();
  late Future<List<Map<String, dynamic>>> _leaderboardFuture;
  bool _showPercentage = false; // Track display mode

  @override
  void initState() {
    super.initState();
    _leaderboardFuture = _attendanceService.getChildrenAttendanceCounts();
  }

  // Calculate ranks with ties based on either count or percentage
  List<Map<String, dynamic>> _calculateRanks(List<Map<String, dynamic>> data, bool usePercentage) {
    // Sort the data based on the selected metric
    if (usePercentage) {
      // Sort by percentage (descending)
      data.sort((a, b) {
        final aCount = a['attendanceCount'] as int;
        final bCount = b['attendanceCount'] as int;
        final aTotal = a['totalBookedClasses'] as int? ?? 1;
        final bTotal = b['totalBookedClasses'] as int? ?? 1;
        
        final aPercentage = (aCount / aTotal) * 100;
        final bPercentage = (bCount / bTotal) * 100;
        
        return bPercentage.compareTo(aPercentage);
      });
    } else {
      // Sort by attendance count (descending)
      data.sort((a, b) => (b['attendanceCount'] as int).compareTo(a['attendanceCount'] as int));
    }
    
    // Calculate ranks with ties
    final List<Map<String, dynamic>> ranked = [];
    int currentRank = 1;
    double previousValue = -1;
    
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final int count = item['attendanceCount'];
      final int total = item['totalBookedClasses'] ?? 1;
      
      // Calculate the value to compare (either count or percentage)
      final double currentValue = usePercentage 
          ? (count / total) * 100 
          : count.toDouble();
      
      if (i > 0 && currentValue < previousValue) {
        // If value is less than previous, update rank
        currentRank = i + 1;
      }
      
      ranked.add({
        'rank': currentRank,
        ...item, // Include other data
      });
      
      previousValue = currentValue;
    }
    
    return ranked;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Attendance Leaderboard',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        elevation: 0, // Remove shadow for modern look
        centerTitle: true,
        actions: [
          // Toggle button in app bar
          IconButton(
            icon: Icon(_showPercentage ? Icons.numbers : Icons.percent),
            onPressed: () {
              setState(() {
                _showPercentage = !_showPercentage;
              });
            },
            tooltip: _showPercentage ? 'Show Count' : 'Show Percentage',
          ),
        ],
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _leaderboardFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5E3C)),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading leaderboard',
                    style: TextStyle(color: Colors.red[300]),
                  ),
                ],
              ),
            );
          }

          final leaderboard = snapshot.data ?? [];

          if (leaderboard.isEmpty) {
            return const Center(
              child: Text('No attendance data available'),
            );
          }

          // Calculate ranks with ties
          final rankedLeaderboard = _calculateRanks(leaderboard, _showPercentage);
          
          // Top 3 for podium
          final top3 = rankedLeaderboard.take(3).toList();
          final rest = rankedLeaderboard.skip(3).toList();

          return SingleChildScrollView(
            child: Column(
              children: [
                // Toggle switch for display mode
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (_showPercentage) {
                              setState(() {
                                _showPercentage = false;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: !_showPercentage ? const Color(0xFF8B5E3C) : Colors.transparent,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.numbers,
                                  size: 16,
                                  color: !_showPercentage ? Colors.white : Colors.grey,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Total Classes',
                                  style: TextStyle(
                                    color: !_showPercentage ? Colors.white : Colors.grey,
                                    fontWeight: !_showPercentage ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (!_showPercentage) {
                              setState(() {
                                _showPercentage = true;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: _showPercentage ? const Color(0xFF8B5E3C) : Colors.transparent,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.percent,
                                  size: 16,
                                  color: _showPercentage ? Colors.white : Colors.grey,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Attendance Rate',
                                  style: TextStyle(
                                    color: _showPercentage ? Colors.white : Colors.grey,
                                    fontWeight: _showPercentage ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Podium section
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.emoji_events, color: Color(0xFFD4AF37), size: 20),
                          const SizedBox(width: 8),
                          Text(
                            _showPercentage ? 'Top Attendance Rates' : 'Most Classes Attended',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF8B5E3C),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (top3.length > 1) _buildPodium(top3[1], top3[1]['rank']),
                          if (top3.isNotEmpty) _buildPodium(top3[0], top3[0]['rank']),
                          if (top3.length > 2) _buildPodium(top3[2], top3[2]['rank']),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // List with tied ranks
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            const Icon(Icons.format_list_numbered, color: Color(0xFF8B5E3C), size: 20),
                            const SizedBox(width: 8),
                            const Text(
                              'Full Rankings',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF8B5E3C),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Divider(height: 1),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: rankedLeaderboard.length,
                        separatorBuilder: (context, index) => const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final childWithRank = rankedLeaderboard[index];
                          return _buildLeaderboardRow(
                            rank: childWithRank['rank'],
                            name: childWithRank['fullName'],
                            avatarPath: childWithRank['avatarPath'],
                            attendanceCount: childWithRank['attendanceCount'],
                            totalBookedClasses: childWithRank['totalBookedClasses'] ?? 1,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPodium(Map<String, dynamic> child, int place) {
    final colors = [
      const Color(0xFFD4AF37), // 1st - gold
      const Color(0xFFC0C0C0), // 2nd - silver
      const Color(0xFFCD7F32), // 3rd - bronze
    ];

    // Define heights based on rank
    double getHeightForRank(int rank) {
      if (rank == 1) return 110.0;
      if (rank == 2) return 90.0;
      if (rank == 3) return 70.0;
      return 70.0; // Fallback
    }

    // Define rank labels
    String getRankLabel(int rank) {
      if (rank == 1) return '1st';
      if (rank == 2) return '2nd';
      if (rank == 3) return '3rd';
      return ''; // Should not happen for podium
    }

    final int calculatedRank = child['rank']; // Use the calculated rank with ties
    
    // Get attendance data
    final int attendanceCount = child['attendanceCount'];
    final int totalBookedClasses = child['totalBookedClasses'] ?? 1; // Default to 1 to avoid division by zero
    final double percentage = (attendanceCount / totalBookedClasses) * 100;
    
    // Display value based on toggle state
    final String displayValue = _showPercentage 
        ? '${percentage.toStringAsFixed(0)}%' 
        : '$attendanceCount';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            getRankLabel(calculatedRank),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C), 
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: colors[calculatedRank - 1],
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: colors[calculatedRank - 1].withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: calculatedRank == 1 ? 36 : 30,
              backgroundColor: const Color(0xFFF9EBD7),
              backgroundImage: child['avatarPath'] != null ? AssetImage(child['avatarPath']) : null,
              child: child['avatarPath'] == null
                  ? Text(
                      child['fullName'][0].toUpperCase(),
                      style: TextStyle(
                        fontSize: calculatedRank == 1 ? 28 : 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF8B5E3C),
                      ),
                    )
                  : null,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 48,
            height: getHeightForRank(calculatedRank),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colors[calculatedRank - 1],
                  colors[calculatedRank - 1].withOpacity(0.7),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              boxShadow: [
                BoxShadow(
                  color: colors[calculatedRank - 1].withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  displayValue,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                if (calculatedRank == 1)
                  const Icon(Icons.star, color: Colors.white, size: 16),
              ],
            ),
          ),
          const SizedBox(height: 4),
          SizedBox(
            width: 80,
            child: Text(
              child['fullName'],
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardRow({
    required int rank,
    required String name,
    required String? avatarPath,
    required int attendanceCount,
    int totalBookedClasses = 1, // Default to 1 to avoid division by zero
  }) {
    // Calculate percentage
    final double percentage = (attendanceCount / totalBookedClasses) * 100;
    
    // Display value based on toggle state
    final String displayValue = _showPercentage 
        ? '${percentage.toStringAsFixed(0)}%' 
        : '$attendanceCount';

    // Get medal emoji based on rank
    String getMedalEmoji(int rank) {
      if (rank == 1) return '🥇';
      if (rank == 2) return '🥈';
      if (rank == 3) return '🥉';
      return '';
    }

    final String medalEmoji = getMedalEmoji(rank);
    final bool isTopThree = rank <= 3;
    final Color rankColor = isTopThree 
        ? rank == 1 
            ? const Color(0xFFD4AF37) // Gold
            : rank == 2 
                ? const Color(0xFFC0C0C0) // Silver
                : const Color(0xFFCD7F32) // Bronze
        : Colors.grey;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isTopThree ? rankColor.withOpacity(0.1) : Colors.grey[100],
            shape: BoxShape.circle,
            border: isTopThree 
                ? Border.all(color: rankColor, width: 1.5)
                : null,
          ),
          child: Center(
            child: isTopThree
                ? Text(
                    medalEmoji,
                    style: const TextStyle(fontSize: 16),
                  )
                : Text(
                    rank.toString(),
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: const Color(0xFFF9EBD7),
              backgroundImage: avatarPath != null ? AssetImage(avatarPath) : null,
              child: avatarPath == null 
                  ? Text(
                      name[0].toUpperCase(), 
                      style: const TextStyle(
                        fontSize: 12, 
                        color: Color(0xFF8B5E3C),
                      ),
                    ) 
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF8B5E3C),
                ),
              ),
            ),
          ],
        ),
        subtitle: Text(
          _showPercentage 
              ? '$attendanceCount of $totalBookedClasses classes' 
              : '${percentage.toStringAsFixed(0)}% attendance rate',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isTopThree 
                ? rankColor.withOpacity(0.1)
                : const Color(0xFF8B5E3C).withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            displayValue,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isTopThree ? rankColor : const Color(0xFF8B5E3C),
            ),
          ),
        ),
      ),
    );
  }
}
