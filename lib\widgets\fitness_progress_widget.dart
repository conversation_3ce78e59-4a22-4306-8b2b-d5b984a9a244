import 'package:flutter/material.dart';
import 'package:gymgo/service/schedule_service.dart';
import 'package:gymgo/service/attendance_service.dart';
import 'package:gymgo/schedule/gym_class.dart';

class FitnessProgressWidget extends StatefulWidget {
  const FitnessProgressWidget({super.key});

  @override
  State<FitnessProgressWidget> createState() => _FitnessProgressWidgetState();
}

class _FitnessProgressWidgetState extends State<FitnessProgressWidget> {
  final ScheduleService _scheduleService = ScheduleService();
  final AttendanceService _attendanceService = AttendanceService();

  List<GymClass> _pastClasses = [];
  List<GymClass> _attendedClasses = [];
  List<GymClass> _missedClasses = [];
  Map<String, bool> _attendanceMap = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPastClasses();
    _loadAttendance();
  }

  void _loadAttendance() {
     _attendanceService.getUserAttendance().listen((attendanceMap) {
      setState(() {
        _attendanceMap = attendanceMap;
        _filterClassesByAttendance();
      });
    });
  }

  Future<void> _loadPastClasses() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final bookedClassIds = await _scheduleService.getBookedClassesForUser();
      final allClasses = await _scheduleService.getClasses().first;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      List<GymClass> allPastBookedClasses = allClasses
            .where((gymClass) => bookedClassIds.contains(gymClass.id))
            .where((gymClass) =>
                gymClass.date.isBefore(today))
            .toList()
          ..sort((a, b) => b.date.isAtSameMomentAs(a.date) ? b.time.compareTo(a.time) : b.date.compareTo(a.date));

      setState(() {
         _pastClasses = allPastBookedClasses;
        _filterClassesByAttendance();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterClassesByAttendance() {
    _attendedClasses = _pastClasses
        .where((gymClass) => _attendanceMap[gymClass.id] == true)
        .toList();
    _missedClasses = _pastClasses
        .where((gymClass) => _attendanceMap[gymClass.id] != true)
        .toList();
  }

  Future<void> _showAttendanceDialog(GymClass gymClass) async {
    final currentStatus = _attendanceMap[gymClass.id];
    bool? newStatus = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Mark Attendance'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Did you attend ${gymClass.name} on ${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year}?' ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Yes'),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('No'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );

    if (newStatus != null && newStatus != currentStatus) {
      try {
        await _attendanceService.markAttendance(gymClass.id!, newStatus);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newStatus ? 'Attendance marked as present' : 'Attendance marked as absent'),
            backgroundColor: newStatus ? Colors.green : Colors.red,
          ),
        );
         // Refresh data after marking attendance
        _loadPastClasses();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error marking attendance: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Fitness Progress',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF8B5E3C),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.refresh, color: Color(0xFF8B5E3C)),
                onPressed: () {
                  _loadPastClasses();
                  _loadAttendance();
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Metrics Section (Adjusted)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildProgressItem(
                  Icons.history, 
                  '${_pastClasses.length}',
                  'Past Classes'),
               _buildProgressItem(
                  Icons.check_circle_outline, 
                  '${_attendedClasses.length}',
                  'Classes Attended'),
               _buildProgressItem(
                  Icons.cancel_outlined, 
                  '${_missedClasses.length}',
                  'Classes Missed'),
            ],
          ),
          const SizedBox(height: 24),

          // Attendance Subtitle
          const Text(
            'Attendance',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
          const SizedBox(height: 16),

          // Section for attended classes
          const Text(
            'Attended Classes',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
           const SizedBox(height: 8),

          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_error != null)
            Center(
              child: Text(
                'Error: $_error',
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            )
          else if (_attendedClasses.isEmpty)
            const Center(
              child: Text(
                'No attended classes to display.',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            )
          else
            Column(
              children: _attendedClasses.map((gymClass) => _buildClassItem(gymClass)).toList(),
            ),

          const SizedBox(height: 24),

          // Section for missed classes
          const Text(
            'Missed Classes',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
           const SizedBox(height: 8),

          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_error != null)
            Center(
              child: Text(
                'Error: $_error',
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            )
          else if (_missedClasses.isEmpty)
            const Center(
              child: Text(
                'No missed classes to display.',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            )
          else
            Column(
              children: _missedClasses.map((gymClass) => _buildClassItem(gymClass)).toList(),
            ),
        ],
      ),
    );
  }

   Widget _buildProgressItem(IconData icon, String value, String label) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: const Color(0xFFF9EBD7),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: const Color(0xFF8B5E3C)),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF8B5E3C),
              ),
            ),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClassItem(GymClass gymClass) {
    final attendanceStatus = _attendanceMap[gymClass.id];
    final bool showAttendanceButton = attendanceStatus == null;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            decoration: BoxDecoration(
              color: attendanceStatus == true
                  ? Colors.green
                  : (attendanceStatus == false
                      ? Colors.red
                      : Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  gymClass.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Color(0xFF8B5E3C),
                  ),
                ),
                const SizedBox(height: 4),
                 Text(
                  '${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year} at ${gymClass.time}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Instructor: ${gymClass.instructor} • Duration: ${gymClass.duration}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                if (attendanceStatus != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Attendance: ${attendanceStatus ? 'Present' : 'Absent'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: attendanceStatus ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (showAttendanceButton)
            Column(
              children: [
                 IconButton(
                  icon: const Icon(Icons.how_to_reg, color: Color(0xFF8B5E3C)),
                  onPressed: () => _showAttendanceDialog(gymClass),
                  tooltip: 'Mark Attendance',
                ),
                const Text('Mark', style: TextStyle(fontSize: 10, color: Color(0xFF8B5E3C)))
              ],
            )
           else if (attendanceStatus != null) ...[
            Icon(
               attendanceStatus == true ? Icons.check_circle : Icons.cancel,
               color: attendanceStatus == true ? Colors.green : Colors.red,
               size: 24,
             ),
           ]
        ],
      ),
    );
  }
} 