//select_screen_class.dart
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:gymgo/payment/admin_payment.dart';
import 'package:intl/intl.dart';
import '../schedule/gym_class.dart'; // Import the GymClass model

class SelectClassScreen extends StatefulWidget {
  const SelectClassScreen({super.key});

  @override
  _SelectClassScreenState createState() => _SelectClassScreenState();
}

class _SelectClassScreenState extends State<SelectClassScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Optionally load initial data or perform setup
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<List<Map<String, dynamic>>> _fetchAllClasses() async {
    try {
      final snapshot = await _firestore.collection('gym_classes').get();
      return snapshot.docs.map((doc) => {'id': doc.id, ...doc.data()}).toList();
    } catch (e) {
      print("Error fetching classes: $e");
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Select Class',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by class name or instructor...',
                prefixIcon: const Icon(Icons.search, color: Color(0xFF8B5E3C)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: _fetchAllClasses(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF8B5E3C),
                      ),
                    ),
                  );
                }
                if (snapshot.hasError) {
                  return Center(
                    child: Text('Error loading classes: ${snapshot.error}'),
                  );
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.class_, size: 100, color: Color(0xFF8B5E3C)),
                        SizedBox(height: 24),
                        Text(
                          'No classes found',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final classes = snapshot.data!;
                final filteredClasses =
                    classes.where((classData) {
                      final className =
                          (classData['name'] ?? '').toString().toLowerCase();
                      final instructor =
                          (classData['instructor'] ?? '')
                              .toString()
                              .toLowerCase();
                      final query = _searchQuery.toLowerCase();
                      return className.contains(query) ||
                          instructor.contains(query);
                    }).toList();

                return ListView.builder(
                  itemCount: filteredClasses.length,
                  itemBuilder: (context, index) {
                    final classData = filteredClasses[index];
                    final className = classData['name'] ?? 'Unknown Class';
                    final instructor =
                        classData['instructor'] ?? 'Unknown Instructor';
                    final time = classData['time'] ?? '';
                    final capacity = classData['capacity'] ?? 0;
                    final enrolled = classData['enrolled'] ?? 0;
                    final payment = classData['payment'] ?? 0;

                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 3,
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16),
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFFEFEDE7),
                          child: Icon(
                            Icons.fitness_center,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                        title: Text(
                          className,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF8B5E3C),
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.person_outline,
                                  size: 16,
                                  color: Color(0xFF8B5E3C),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Instructor: $instructor',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 16,
                                  color: Color(0xFF8B5E3C),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Date: ${classData['date'] != null ? classData['date'].toString().split('T').first : 'N/A'}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 16,
                                  color: Color(0xFF8B5E3C),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Time: $time',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.people,
                                  size: 16,
                                  color: Color(0xFF8B5E3C),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Capacity: $enrolled/$capacity',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.attach_money,
                                  size: 16,
                                  color: Color(0xFF8B5E3C),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Payment: \$${payment.toString()}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                          ],
                        ),
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          color: Color(0xFF8B5E3C),
                          size: 16,
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => AdminPaymentScreen(
                                    classId: classData['id'],
                                    className: className,
                                    classPayment: payment.toString(),
                                  ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
