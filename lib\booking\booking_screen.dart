import 'package:flutter/material.dart';
import '../schedule/gym_class.dart';
import '../service/schedule_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

// A screen that displays available fitness classes and allows users to book them
class BookingScreen extends StatefulWidget {
  const BookingScreen({super.key});

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  // Service to handle class-related operations
  final ScheduleService _scheduleService = ScheduleService();

  // List to store all classes and filtered classes
  List<GymClass> _classes = [];
  List<GymClass> _filteredClasses = [];

  // State variables for loading and error handling
  bool _isLoading = true;
  String? _error;

  // Controller for the search text field
  final TextEditingController _searchController = TextEditingController();

  // Save the booked class ID
  Set<String> _bookedClassIds = {};

  // Children data
  List<Map<String, dynamic>> _children = [];
  Map<String, Set<String>> _childBookedClassIds = {};
  bool _isChildrenLoading = true;

  @override
  void initState() {
    super.initState();
    _loadClasses();
    _loadBookedClasses();
    _loadChildrenAndBookings();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Loads all available classes from Firebase
  void _loadClasses() {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _scheduleService.getClasses().listen(
        (classes) {
          // Sort classes by date ascending (earlier dates first)
          final sortedClasses = List<GymClass>.from(classes)
            ..sort((a, b) => a.date.compareTo(b.date));
          setState(() {
            _classes = sortedClasses;
            _filteredClasses = sortedClasses;
            _isLoading = false;
          });
        },
        onError: (error) {
          setState(() {
            _error = error.toString();
            _isLoading = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// Load the set of class IDs the current user has booked from Firestore
  Future<void> _loadBookedClasses() async {
    final booked = await _scheduleService.getBookedClassesForUser();
    setState(() {
      _bookedClassIds = booked;
    });
  }

  // Load all children and their booked classes
  Future<void> _loadChildrenAndBookings() async {
    setState(() {
      _isChildrenLoading = true;
    });
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final childrenSnapshot =
        await FirebaseFirestore.instance
            .collection('children')
            .where('userId', isEqualTo: user.uid)
            .get();
    List<Map<String, dynamic>> children = [];
    Map<String, Set<String>> childBooked = {};
    for (var doc in childrenSnapshot.docs) {
      final data = doc.data();
      data['id'] = doc.id;
      children.add(data);
      final booked = await _scheduleService.getBookedClassesForChild(doc.id);
      childBooked[doc.id] = booked;
    }
    setState(() {
      _children = children;
      _childBookedClassIds = childBooked;
      _isChildrenLoading = false;
    });
  }

  // Filters classes based on the search query
  void _filterClasses(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredClasses = List<GymClass>.from(_classes);
      } else {
        _filteredClasses = _classes.where((class_) {
          return class_.name.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
      // Sort filtered classes by date ascending (earlier dates first)
      _filteredClasses.sort((a, b) => a.date.compareTo(b.date));
    });
  }

  // Check if all children have booked this class
  bool _allChildrenBooked(String classId) {
    if (_children.isEmpty) return false;
    for (var child in _children) {
      final childId = child['id'];
      if (!(_childBookedClassIds[childId]?.contains(classId) ?? false)) {
        return false;
      }
    }
    return true;
  }

  // Check if there is any child who has not booked this class
  bool _hasUnbookedChild(String classId) {
    for (var child in _children) {
      final childId = child['id'];
      if (!(_childBookedClassIds[childId]?.contains(classId) ?? false)) {
        return true;
      }
    }
    return false;
  }

// Check if a class has expired based on its date and time
bool _isClassExpired(GymClass gymClass) {
  final now = DateTime.now();

  try {
    // Combine class date and time into a full DateTime object
    final timeParts = gymClass.time.split(':');
    if (timeParts.length != 2) return false; // Invalid time format, allow booking

    final hour = int.tryParse(timeParts[0]) ?? 0;
    final minute = int.tryParse(timeParts[1]) ?? 0;

    final classDateTime = DateTime(
      gymClass.date.year,
      gymClass.date.month,
      gymClass.date.day,
      hour,
      minute,
    );

    return classDateTime.isBefore(now); // Return true if class has expired
  } catch (e) {
    print('Error in _isClassExpired: ${e.toString()}');
    return false; // On error, assume class is not expired
  }
}

// Show a dialog to select children for booking a class
Future<void> _showBookingConfirmation(GymClass gymClass) async {
  if (_isClassExpired(gymClass)) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This class has expired and cannot be booked.'),
          backgroundColor: Colors.red,
        ),
      );
    }
    return; // Stop booking process
  }

  // Proceed with booking if class is valid
    List<String> selectedChildIds = [];
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.fitness_center,
                      color: Color.fromARGB(255, 211, 169, 137),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Book "${gymClass.name}" for:',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color.fromARGB(255, 70, 70, 70),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_children.isEmpty) const Text('No children found.'),
                    if (_children.isNotEmpty)
                      ..._children.map((child) {
                        final childId = child['id'];
                        final fullName = child['fullName'] ?? 'Unnamed';
                        final booked =
                            _childBookedClassIds[childId]?.contains(
                              gymClass.id,
                            ) ??
                            false;
                        return CheckboxListTile(
                          value: selectedChildIds.contains(childId) || booked,
                          onChanged:
                              booked
                                  ? null
                                  : (checked) {
                                    setState(() {
                                      if (checked == true) {
                                        selectedChildIds.add(childId);
                                      } else {
                                        selectedChildIds.remove(childId);
                                      }
                                    });
                                  },
                          title: Text(fullName),
                          controlAffinity: ListTileControlAffinity.leading,
                          secondary:
                              booked
                                  ? const Icon(Icons.check, color: Colors.green)
                                  : null,
                          subtitle:
                              booked
                                  ? const Text(
                                    'Already booked',
                                    style: TextStyle(color: Colors.grey),
                                  )
                                  : null,
                        );
                      }).toList(),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            foregroundColor: const Color.fromARGB(
                              255,
                              109,
                              109,
                              109,
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed:
                              selectedChildIds.isEmpty
                                  ? null
                                  : () async {
                                    Navigator.of(context).pop();
                                    try {
                                      await _scheduleService
                                          .bookClassForChildren(
                                            gymClass,
                                            selectedChildIds,
                                          );
                                      _loadClasses();
                                      _loadChildrenAndBookings();
                                      if (mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                              'Class booked successfully!',
                                            ),
                                            backgroundColor: Color.fromARGB(
                                              255,
                                              205,
                                              148,
                                              104,
                                            ),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Error booking class: ${e.toString()}',
                                            ),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                    }
                                  },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color.fromARGB(
                              255,
                              211,
                              169,
                              137,
                            ),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: const Text('Book'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 18, color: const Color.fromARGB(255, 109, 109, 109)),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color.fromARGB(255, 109, 109, 109),
            ),
          ),
          Text(
            value,
            style: const TextStyle(color: Color.fromARGB(255, 70, 70, 70)),
          ),
        ],
      ),
    );
  }

  Widget _buildCapacityInfo(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 211, 169, 137).withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Color.fromARGB(255, 109, 109, 109),
            ),
          ),
          Text(
            value.toString(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Color.fromARGB(255, 70, 70, 70),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Book Classes',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Color.fromARGB(255, 80, 80, 80),
          ),
        ),
        backgroundColor: const Color.fromARGB(255, 211, 169, 137),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 12),
            child: IconButton(
              icon: Icon(Icons.calendar_month, size: 32),
              tooltip: 'View Children Schedule',
              onPressed: () {
                showDialog(
                  context: context,
                  builder:
                      (context) => ChildrenScheduleDialog(
                        children: _children,
                        parentId: FirebaseAuth.instance.currentUser!.uid,
                        onBookingChanged: _loadChildrenAndBookings,
                        rootScaffoldMessenger: ScaffoldMessenger.of(context),
                      ),
                );
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 211, 169, 137),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search class name...',
                hintStyle: const TextStyle(color: Colors.white70),
                prefixIcon: const Icon(Icons.search, color: Colors.white),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: const Color.fromARGB(255, 205, 148, 104),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: _filterClasses,
            ),
          ),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _error != null
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Error: $_error',
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadClasses,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color.fromARGB(
                                255,
                                211,
                                169,
                                137,
                              ),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                    : _filteredClasses.isEmpty
                    ? const Center(
                      child: Text(
                        'No classes found.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: Color.fromARGB(255, 109, 109, 109),
                        ),
                      ),
                    )
                    : RefreshIndicator(
                      onRefresh: () async {
                        _loadClasses();
                      },
                      child: ListView(
                        padding: const EdgeInsets.all(16),
                        children: [
                          // Upcoming Classes Section
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 8),
                            child: Text(
                              'Upcoming Classes',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color.fromARGB(255, 109, 109, 109),
                              ),
                            ),
                          ),
                          ..._filteredClasses
                              .where((class_) => !class_.date.isBefore(DateTime.now()))
                              .map((gymClass) => Card(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    elevation: 4,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            const Color.fromARGB(255, 255, 255, 255),
                                            const Color.fromARGB(255, 239, 232, 222),
                                          ],
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    gymClass.name,
                                                    style: const TextStyle(
                                                      fontSize: 20,
                                                      fontWeight: FontWeight.bold,
                                                      color: Color.fromARGB(255, 70, 70, 70),
                                                    ),
                                                  ),
                                                ),
                                                ElevatedButton(
                                                  onPressed:
                                                      _isChildrenLoading ||
                                                              _allChildrenBooked(
                                                                gymClass.id!,
                                                              )
                                                          ? null
                                                          : () {
                                                            _showBookingConfirmation(
                                                              gymClass,
                                                            );
                                                          },
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        const Color.fromARGB(
                                                          255,
                                                          211,
                                                          169,
                                                          137,
                                                        ),
                                                    foregroundColor: Colors.white,
                                                    padding: const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 8,
                                                    ),
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(10),
                                                    ),
                                                  ),
                                                  child: Text(
                                                    _allChildrenBooked(gymClass.id!)
                                                        ? 'Booked'
                                                        : 'Book',
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 12),
                                            _buildInfoRow(
                                              Icons.person,
                                              'Instructor',
                                              gymClass.instructor,
                                            ),
                                            _buildInfoRow(
                                              Icons.calendar_today,
                                              'Date',
                                              '${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year}',
                                            ),
                                            _buildInfoRow(
                                              Icons.access_time,
                                              'Time',
                                              gymClass.time,
                                            ),
                                            _buildInfoRow(
                                              Icons.timer,
                                              'Duration',
                                              gymClass.duration,
                                            ),
                                            _buildInfoRow(
                                              Icons.attach_money,
                                              'Payment',
                                              (gymClass.payment == null ||
                                                      gymClass.payment == 0)
                                                  ? 'Free'
                                                  : gymClass.payment!.toStringAsFixed(
                                                    2,
                                                  ),
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                _buildCapacityInfo(
                                                  'Capacity',
                                                  gymClass.capacity,
                                                ),
                                                _buildCapacityInfo(
                                                  'Enrolled',
                                                  gymClass.enrolled,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ))
                              .toList(),
                          const SizedBox(height: 24),
                          // Past Classes Section
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 8),
                            child: Text(
                              'Past Classes',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color.fromARGB(255, 109, 109, 109),
                              ),
                            ),
                          ),
                          ..._filteredClasses
                              .where((class_) => class_.date.isBefore(DateTime.now()))
                              .map((gymClass) => Card(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    elevation: 4,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            const Color.fromARGB(255, 255, 255, 255),
                                            const Color.fromARGB(255, 239, 232, 222),
                                          ],
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    gymClass.name,
                                                    style: const TextStyle(
                                                      fontSize: 20,
                                                      fontWeight: FontWeight.bold,
                                                      color: Color.fromARGB(255, 70, 70, 70),
                                                    ),
                                                  ),
                                                ),
                                                // No booking button for past classes
                                              ],
                                            ),
                                            const SizedBox(height: 12),
                                            _buildInfoRow(
                                              Icons.person,
                                              'Instructor',
                                              gymClass.instructor,
                                            ),
                                            _buildInfoRow(
                                              Icons.calendar_today,
                                              'Date',
                                              '${gymClass.date.day}/${gymClass.date.month}/${gymClass.date.year}',
                                            ),
                                            _buildInfoRow(
                                              Icons.access_time,
                                              'Time',
                                              gymClass.time,
                                            ),
                                            _buildInfoRow(
                                              Icons.timer,
                                              'Duration',
                                              gymClass.duration,
                                            ),
                                            _buildInfoRow(
                                              Icons.attach_money,
                                              'Payment',
                                              (gymClass.payment == null ||
                                                      gymClass.payment == 0)
                                                  ? 'Free'
                                                  : gymClass.payment!.toStringAsFixed(
                                                    2,
                                                  ),
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                _buildCapacityInfo(
                                                  'Capacity',
                                                  gymClass.capacity,
                                                ),
                                                _buildCapacityInfo(
                                                  'Enrolled',
                                                  gymClass.enrolled,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }
}

class ChildrenScheduleDialog extends StatefulWidget {
  final List<Map<String, dynamic>> children;
  final String parentId;
  final VoidCallback? onBookingChanged;
  final ScaffoldMessengerState rootScaffoldMessenger;
  const ChildrenScheduleDialog({
    required this.children,
    required this.parentId,
    this.onBookingChanged,
    required this.rootScaffoldMessenger,
    super.key,
  });

  @override
  State<ChildrenScheduleDialog> createState() => _ChildrenScheduleDialogState();
}

class _ChildrenScheduleDialogState extends State<ChildrenScheduleDialog> {
  String? _selectedChildId;
  String? _selectedChildName;

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      child: Builder(
        builder:
            (dialogContext) => Scaffold(
              backgroundColor: Colors.transparent,
              body: AlertDialog(
                title: Row(
                  children: [
                    const Icon(
                      Icons.calendar_month,
                      color: Color.fromARGB(255, 211, 169, 137),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Children Schedule',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color.fromARGB(255, 70, 70, 70),
                      ),
                    ),
                  ],
                ),
                content: Container(
                  width: 400,
                  constraints: const BoxConstraints(maxHeight: 600),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(
                            255,
                            211,
                            169,
                            137,
                          ).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButton<String>(
                          value: _selectedChildId,
                          hint: const Text('Select a child'),
                          isExpanded: true,
                          underline: const SizedBox(),
                          items:
                              widget.children.map((child) {
                                return DropdownMenuItem<String>(
                                  value: child['id'],
                                  child: Text(
                                    child['fullName'] ?? 'Unnamed',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Color.fromARGB(255, 70, 70, 70),
                                    ),
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedChildId = value;
                              _selectedChildName =
                                  widget.children.firstWhere(
                                    (c) => c['id'] == value,
                                  )['fullName'] ??
                                  'Unnamed';
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (_selectedChildId != null)
                        Expanded(
                          child: SingleChildScrollView(
                            child: _ChildScheduleList(
                              parentId: widget.parentId,
                              childId: _selectedChildId!,
                              childName: _selectedChildName ?? '',
                              onBookingChanged: widget.onBookingChanged,
                              dialogScaffoldContext: dialogContext,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      foregroundColor: const Color.fromARGB(255, 109, 109, 109),
                    ),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
      ),
    );
  }
}

class _ChildScheduleList extends StatefulWidget {
  final String parentId;
  final String childId;
  final String childName;
  final VoidCallback? onBookingChanged;
  final BuildContext dialogScaffoldContext;
  const _ChildScheduleList({
    required this.parentId,
    required this.childId,
    required this.childName,
    this.onBookingChanged,
    required this.dialogScaffoldContext,
  });

  @override
  State<_ChildScheduleList> createState() => _ChildScheduleListState();
}

class _ChildScheduleListState extends State<_ChildScheduleList> {
  List<Map<String, dynamic>> _bookedClassDetails = [];
  bool _isLoadingBooked = true;
  bool _showDeleteCustom = false;

  @override
  void initState() {
    super.initState();
    _fetchBookedClasses();
  }

  Future<void> _fetchBookedClasses() async {
    setState(() {
      _isLoadingBooked = true;
    });
    final bookedClassIds = await ScheduleService().getBookedClassesForChild(
      widget.childId,
    );
    if (bookedClassIds.isEmpty) {
      setState(() {
        _bookedClassDetails = [];
        _isLoadingBooked = false;
      });
      return;
    }
    // Fetch class details from gym_classes
    final gymClassesSnap =
        await FirebaseFirestore.instance.collection('gym_classes').get();
    final allClasses =
        gymClassesSnap.docs
            .map((doc) => {'id': doc.id, ...doc.data()})
            .toList();
    final bookedDetails =
        allClasses.where((c) => bookedClassIds.contains(c['id'])).toList();
    setState(() {
      _bookedClassDetails = bookedDetails;
      _isLoadingBooked = false;
    });
  }

  @override
  void didUpdateWidget(covariant _ChildScheduleList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.childId != widget.childId) {
      _fetchBookedClasses();
    }
  }

  @override
  Widget build(BuildContext context) {
    final schedulesRef = FirebaseFirestore.instance
        .collection('children')
        .doc(widget.childId)
        .collection('schedules');
    final now = DateTime.now();
    final upcomingBooked = <Map<String, dynamic>>[];
    final pastBooked = <Map<String, dynamic>>[];
    for (final data in _bookedClassDetails) {
      DateTime? classDate;
      if (data['date'] is String) {
        classDate = DateTime.tryParse(data['date']);
      } else if (data['date'] is Timestamp) {
        classDate = (data['date'] as Timestamp).toDate();
      }
      if (classDate != null && classDate.isAfter(now)) {
        upcomingBooked.add(data);
      } else {
        pastBooked.add(data);
      }
    }

    // Move StreamBuilder to a variable so it is rebuilt with setState
    final customSchedulesSection = StreamBuilder<QuerySnapshot>(
      stream: schedulesRef.snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return const SizedBox.shrink();
        }
        return Column(
          children:
              snapshot.data!.docs.map((doc) {
                final data = doc.data() as Map<String, dynamic>;
                print(
                  'DEBUG: _showDeleteCustom in ListTile = $_showDeleteCustom',
                );
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    title: Text(data['className'] ?? 'No class name'),
                    subtitle: Text(
                      '${data['date'] ?? ''} at ${data['time'] ?? ''}',
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.orange),
                          onPressed: () async {
                            await showDialog(
                              context: context,
                              builder:
                                  (context) => _ScheduleEditDialog(
                                    parentId: widget.parentId,
                                    childId: widget.childId,
                                    scheduleId: doc.id,
                                    initialData: data,
                                  ),
                            );
                          },
                        ),
                        if (_showDeleteCustom)
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            tooltip: 'Cancel Booking',
                            onPressed: () async {
                              final confirm = await showDialog<bool>(
                                context: context,
                                builder:
                                    (context) => AlertDialog(
                                      title: const Text('Cancel Booking'),
                                      content: Text(
                                        'Are you sure you want to cancel the booking for "${data['name']}"?',
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed:
                                              () => Navigator.of(
                                                context,
                                              ).pop(false),
                                          child: const Text('No'),
                                        ),
                                        ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red,
                                            foregroundColor: Colors.white,
                                          ),
                                          onPressed:
                                              () => Navigator.of(
                                                context,
                                              ).pop(true),
                                          child: const Text('Yes'),
                                        ),
                                      ],
                                    ),
                              );
                              if (confirm == true) {
                                final childRef = FirebaseFirestore.instance
                                    .collection('children')
                                    .doc(widget.childId);
                                await childRef.update({
                                  'bookedClasses': FieldValue.arrayRemove([
                                    {
                                      'classId': data['id'],
                                      'className':
                                          data['className'] ?? data['name'],
                                      'payment': data['payment'],
                                    },
                                  ]),
                                });
                                // Update gym_classes: increment capacity, decrement enrolled
                                final classRef = FirebaseFirestore.instance
                                    .collection('gym_classes')
                                    .doc(data['id']);
                                await classRef.update({
                                  'capacity': FieldValue.increment(1),
                                  'enrolled': FieldValue.increment(-1),
                                });
                                setState(() {
                                  _bookedClassDetails.removeWhere(
                                    (c) => c['id'] == data['id'],
                                  );
                                });
                                if (widget.onBookingChanged != null)
                                  widget.onBookingChanged!();
                                await _fetchBookedClasses();
                                // Show popup success dialog
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder:
                                      (context) => AlertDialog(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        backgroundColor: Colors.white,
                                        content: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.check_circle,
                                              color: Colors.green,
                                              size: 48,
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              'Booking cancelled successfully!',
                                              style: TextStyle(
                                                color: Colors.green,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed:
                                                () =>
                                                    Navigator.of(context).pop(),
                                            child: const Text(
                                              'OK',
                                              style: TextStyle(
                                                color: Colors.green,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                );
                              }
                            },
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
        );
      },
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: const Color.fromARGB(255, 211, 169, 137).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "${widget.childName}'s Schedule",
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 70, 70, 70),
                ),
              ),
              IconButton(
                icon: Icon(
                  _showDeleteCustom ? Icons.close : Icons.edit,
                  color: const Color.fromARGB(255, 211, 169, 137),
                ),
                tooltip: _showDeleteCustom ? 'Done' : 'Edit Schedule',
                onPressed: () {
                  setState(() {
                    _showDeleteCustom = !_showDeleteCustom;
                  });
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Booked classes section
        if (_isLoadingBooked)
          const Center(child: CircularProgressIndicator())
        else ...[
          if (upcomingBooked.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Text(
                'Upcoming Booked Classes',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 211, 169, 137),
                ),
              ),
            ),
            ...upcomingBooked.map((data) {
              DateTime? classDate;
              String dateStr = '';
              if (data['date'] is String) {
                classDate = DateTime.tryParse(data['date']);
                if (classDate != null) {
                  dateStr =
                      '${classDate.year}-${classDate.month.toString().padLeft(2, '0')}-${classDate.day.toString().padLeft(2, '0')}';
                } else {
                  dateStr = data['date'];
                }
              } else if (data['date'] is Timestamp) {
                classDate = (data['date'] as Timestamp).toDate();
                dateStr =
                    '${classDate.year}-${classDate.month.toString().padLeft(2, '0')}-${classDate.day.toString().padLeft(2, '0')}';
              }
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color.fromARGB(
                          255,
                          211,
                          169,
                          137,
                        ).withOpacity(0.1),
                      ],
                    ),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(
                          255,
                          211,
                          169,
                          137,
                        ).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: Color.fromARGB(255, 211, 169, 137),
                      ),
                    ),
                    title: Text(
                      data['name'] ?? 'No class name',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color.fromARGB(255, 70, 70, 70),
                      ),
                    ),
                    subtitle: Text(
                      '$dateStr at ${data['time'] ?? ''}',
                      style: const TextStyle(
                        color: Color.fromARGB(255, 109, 109, 109),
                      ),
                    ),
                    trailing:
                        _showDeleteCustom
                            ? IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              tooltip: 'Cancel Booking',
                              onPressed: () async {
                                final confirm = await showDialog<bool>(
                                  context: context,
                                  builder:
                                      (context) => AlertDialog(
                                        title: const Text('Cancel Booking'),
                                        content: Text(
                                          'Are you sure you want to cancel the booking for "${data['name']}"?',
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed:
                                                () => Navigator.of(
                                                  context,
                                                ).pop(false),
                                            child: const Text('No'),
                                          ),
                                          ElevatedButton(
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                              foregroundColor: Colors.white,
                                            ),
                                            onPressed:
                                                () => Navigator.of(
                                                  context,
                                                ).pop(true),
                                            child: const Text('Yes'),
                                          ),
                                        ],
                                      ),
                                );
                                if (confirm == true) {
                                  final childRef = FirebaseFirestore.instance
                                      .collection('children')
                                      .doc(widget.childId);
                                  await childRef.update({
                                    'bookedClasses': FieldValue.arrayRemove([
                                      {
                                        'classId': data['id'],
                                        'className':
                                            data['className'] ?? data['name'],
                                        'payment': data['payment'],
                                      },
                                    ]),
                                  });
                                  // Update gym_classes: increment capacity, decrement enrolled
                                  final classRef = FirebaseFirestore.instance
                                      .collection('gym_classes')
                                      .doc(data['id']);
                                  await classRef.update({
                                    'capacity': FieldValue.increment(1),
                                    'enrolled': FieldValue.increment(-1),
                                  });
                                  setState(() {
                                    _bookedClassDetails.removeWhere(
                                      (c) => c['id'] == data['id'],
                                    );
                                  });
                                  if (widget.onBookingChanged != null)
                                    widget.onBookingChanged!();
                                  await _fetchBookedClasses();
                                  // Show popup success dialog
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder:
                                        (context) => AlertDialog(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              20,
                                            ),
                                          ),
                                          backgroundColor: Colors.white,
                                          content: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.check_circle,
                                                color: Colors.green,
                                                size: 48,
                                              ),
                                              const SizedBox(height: 16),
                                              Text(
                                                'Booking cancelled successfully!',
                                                style: TextStyle(
                                                  color: Colors.green,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed:
                                                  () =>
                                                      Navigator.of(
                                                        context,
                                                      ).pop(),
                                              child: const Text(
                                                'OK',
                                                style: TextStyle(
                                                  color: Colors.green,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                  );
                                }
                              },
                            )
                            : null,
                  ),
                ),
              );
            }),
          ],
          if (pastBooked.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.only(top: 16, bottom: 8),
              child: Text(
                'Past Booked Classes',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 109, 109, 109),
                ),
              ),
            ),
            ...pastBooked.map((data) {
              DateTime? classDate;
              String dateStr = '';
              if (data['date'] is String) {
                classDate = DateTime.tryParse(data['date']);
                if (classDate != null) {
                  dateStr =
                      '${classDate.year}-${classDate.month.toString().padLeft(2, '0')}-${classDate.day.toString().padLeft(2, '0')}';
                } else {
                  dateStr = data['date'];
                }
              } else if (data['date'] is Timestamp) {
                classDate = (data['date'] as Timestamp).toDate();
                dateStr =
                    '${classDate.year}-${classDate.month.toString().padLeft(2, '0')}-${classDate.day.toString().padLeft(2, '0')}';
              }
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: const Color(0xFFF5F5F5),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.history, color: Colors.grey),
                    ),
                    title: Text(
                      data['name'] ?? 'No class name',
                      style: const TextStyle(
                        color: Color.fromARGB(255, 109, 109, 109),
                      ),
                    ),
                    subtitle: Text(
                      '$dateStr at ${data['time'] ?? ''}',
                      style: const TextStyle(
                        color: Color.fromARGB(255, 109, 109, 109),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ],
        ],
        const SizedBox(height: 16),
        // Custom schedules section
        customSchedulesSection,
      ],
    );
  }
}

class _ScheduleEditDialog extends StatefulWidget {
  final String parentId;
  final String childId;
  final String? scheduleId;
  final Map<String, dynamic>? initialData;
  const _ScheduleEditDialog({
    required this.parentId,
    required this.childId,
    this.scheduleId,
    this.initialData,
    super.key,
  });

  @override
  State<_ScheduleEditDialog> createState() => _ScheduleEditDialogState();
}

class _ScheduleEditDialogState extends State<_ScheduleEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _classNameController;
  late TextEditingController _dateController;
  late TextEditingController _timeController;

  @override
  void initState() {
    super.initState();
    _classNameController = TextEditingController(
      text: widget.initialData?['className'] ?? '',
    );
    _dateController = TextEditingController(
      text: widget.initialData?['date'] ?? '',
    );
    _timeController = TextEditingController(
      text: widget.initialData?['time'] ?? '',
    );
  }

  @override
  void dispose() {
    _classNameController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.scheduleId == null ? 'Add Schedule' : 'Edit Schedule'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _classNameController,
              decoration: const InputDecoration(labelText: 'Class Name'),
              validator:
                  (value) =>
                      value == null || value.isEmpty
                          ? 'Enter class name'
                          : null,
            ),
            TextFormField(
              controller: _dateController,
              decoration: const InputDecoration(labelText: 'Date (YYYY-MM-DD)'),
              validator:
                  (value) =>
                      value == null || value.isEmpty ? 'Enter date' : null,
              onTap: () async {
                FocusScope.of(context).requestFocus(FocusNode());
                final picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2100),
                );
                if (picked != null) {
                  _dateController.text =
                      picked.toIso8601String().split('T').first;
                }
              },
            ),
            TextFormField(
              controller: _timeController,
              decoration: const InputDecoration(labelText: 'Time (e.g. 15:00)'),
              validator:
                  (value) =>
                      value == null || value.isEmpty ? 'Enter time' : null,
              onTap: () async {
                FocusScope.of(context).requestFocus(FocusNode());
                final picked = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );
                if (picked != null) {
                  _timeController.text = picked.format(context);
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (_formKey.currentState!.validate()) {
              final data = {
                'className': _classNameController.text,
                'date': _dateController.text,
                'time': _timeController.text,
              };
              final schedulesRef = FirebaseFirestore.instance
                  .collection('children')
                  .doc(widget.childId)
                  .collection('schedules');
              if (widget.scheduleId == null) {
                await schedulesRef.add(data);
              } else {
                await schedulesRef.doc(widget.scheduleId).update(data);
              }
              if (mounted) Navigator.of(context).pop();
            }
          },
          child: Text(widget.scheduleId == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}
