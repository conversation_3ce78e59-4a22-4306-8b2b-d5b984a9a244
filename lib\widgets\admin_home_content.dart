import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:intl/intl.dart';
import 'fitness_journey_widget.dart';
import 'child_avatar.dart';

class AdminHomeContent extends StatefulWidget {
  const AdminHomeContent({super.key});

  @override
  State<AdminHomeContent> createState() => _AdminHomeContentState();
}

class _AdminHomeContentState extends State<AdminHomeContent> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  List<Map<String, dynamic>> _children = [];
  List<Map<String, dynamic>> _filteredChildren = [];

  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  
  // Overview data
  int _totalChildren = 0;
  int _totalClasses = 0;
  int _totalAttendances = 0;
  int _todayClasses = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load children data
      final childrenSnapshot = await _firestore.collection('children').get();
      final List<Map<String, dynamic>> children = [];
      
      for (var doc in childrenSnapshot.docs) {
        try {
          final data = doc.data();
          if (data != null) {
            data['id'] = doc.id;

            // Get parent name
            final parentId = data['userId']; // Assume userId field in child document points to parent user ID
            String parentName = 'Unknown';
            if (parentId != null) {
              final parentDoc = await _firestore.collection('users').doc(parentId).get();
              if (parentDoc.exists && parentDoc.data() != null) {
                parentName = parentDoc.data()!['fullName'] ?? parentDoc.data()!['name'] ?? 'Unknown';
              }
            }
            data['parentName'] = parentName; // Add parent name to child data
            children.add(data);
          }
        } catch (e) {
          print('Error loading child with ID ${doc.id}: $e');
        }
      }

      // Load class data
      final classesSnapshot = await _firestore.collection('gym_classes').get();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      // Calculate today's classes
      int todayClasses = 0;

      for (var doc in classesSnapshot.docs) {
        try {
          final data = doc.data();
          if (data != null && data['date'] != null) {
            DateTime? classDate;

            if (data['date'] is Timestamp) {
              classDate = (data['date'] as Timestamp).toDate(); // Class date parsed as Timestamp
            } else if (data['date'] is String) {
              try {
                classDate = DateTime.parse(data['date']);
              } catch (e) {
                print('Error parsing class date (String) for class ID ${doc.id}: $e');
              }
            } 

            if (classDate != null) {
              final isToday = classDate.year == today.year &&
                              classDate.month == today.month &&
                              classDate.day == today.day;
              if (isToday) {
                todayClasses++;
              }
            }
          }
        } catch (e) {
          print('Error loading class with ID ${doc.id}: $e');
        }
      }

      // Load attendance records (from top-level attendance collection)
      final allAttendanceSnapshot = await _firestore.collection('attendance').get();
      int totalAttendances = allAttendanceSnapshot.docs.length;

      setState(() {
        _children = children;
        _filteredChildren = children;
        _totalChildren = children.length;
        _totalClasses = classesSnapshot.docs.length;
        _totalAttendances = totalAttendances;
        _todayClasses = todayClasses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterChildren(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredChildren = _children;
      } else {
        _filteredChildren = _children.where((child) {
          final childName = (child['fullName'] ?? child['name'] ?? '').toString().toLowerCase();
          final parentName = (child['parentName'] ?? '').toString().toLowerCase();
          return childName.contains(query.toLowerCase()) || 
                 parentName.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  // Build overview summary card
  Widget _buildOverviewCard(IconData icon, String title, String value, Color bgColor) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: const Color(0xFF8B5E3C)),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF8B5E3C),
              ),
            ),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build child list card
  Widget _buildChildCard(Map<String, dynamic> child) {
    final childName = child['fullName'] ?? child['name'] ?? 'Unknown Child';
    final parentName = child['parentName'] ?? 'Unknown';
    final childId = child['id'] ?? '';
    final avatarPath = child['avatarPath'] as String?;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: ListTile(
        leading: buildChildAvatar(
          avatarPath: avatarPath,
          displayName: childName,
          radius: 24,
          imageSize: 48,
        ),
        title: Text(
          childName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Color(0xFF8B5E3C),
          ),
        ),
        subtitle: Text(
          'Parent: $parentName',
          style: const TextStyle(color: Colors.grey),
        ),
        trailing: ElevatedButton(
          onPressed: childId.isNotEmpty ? () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => FitnessJourneyWidget(
                  childId: childId,
                  childName: childName,
                ),
              ),
            );
          } : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF8B5E3C),
            foregroundColor: Colors.white,
          ),
          child: const Text('View Journey'),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Text(
          'Error: $_error',
          style: const TextStyle(color: Colors.red),
          textAlign: TextAlign.center,
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dashboard summary
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Admin Dashboard',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF8B5E3C),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh, color: Color(0xFF8B5E3C)),
                      onPressed: _loadData,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildOverviewCard(
                      Icons.child_care,
                      'Total Children',
                      '$_totalChildren',
                      const Color.fromARGB(255, 255, 215, 159),
                    ),
                    _buildOverviewCard(
                      Icons.fitness_center,
                      'Total Classes',
                      '$_totalClasses',
                      const Color(0xFFE0C3A3),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildOverviewCard(
                      Icons.check_circle,
                      'Total Attendances',
                      '$_totalAttendances',
                      const Color(0xFFD4E4C5),
                    ),
                    _buildOverviewCard(
                      Icons.today,
                      'Today\'s Classes',
                      '$_todayClasses',
                      const Color(0xFFF8D7DA),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Children list area
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Children\'s Fitness Journey',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF8B5E3C),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  onChanged: _filterChildren,
                  decoration: InputDecoration(
                    hintText: 'Search by child or parent name...',
                    prefixIcon: const Icon(Icons.search, color: Color(0xFF8B5E3C)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                if (_filteredChildren.isEmpty)
                  const Center(
                    child: Text(
                      'No children found.',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _filteredChildren.length,
                    itemBuilder: (context, index) => _buildChildCard(_filteredChildren[index]),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
