// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDciMEv14G1pYQjvCL8cfgC8nI0JMBMUzA',
    appId: '1:571328978605:web:7a0bae749a15dbeb503024',
    messagingSenderId: '571328978605',
    projectId: 'gymgo-5f30f',
    authDomain: 'gymgo-5f30f.firebaseapp.com',
    storageBucket: 'gymgo-5f30f.firebasestorage.app',
    measurementId: 'G-542BG2QXMF',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD_xDhfZdz4cFVtWHBSJYLwDi4u6W6ODaU',
    appId: '1:571328978605:android:6ece05b7ad24e1fc503024',
    messagingSenderId: '571328978605',
    projectId: 'gymgo-5f30f',
    storageBucket: 'gymgo-5f30f.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAZLigYeEP3kmGqED7oLNVpxnjaUb8ENZs',
    appId: '1:571328978605:ios:7c852bb0f49a61a7503024',
    messagingSenderId: '571328978605',
    projectId: 'gymgo-5f30f',
    storageBucket: 'gymgo-5f30f.firebasestorage.app',
    iosBundleId: 'com.example.gymgo',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAZLigYeEP3kmGqED7oLNVpxnjaUb8ENZs',
    appId: '1:571328978605:ios:7c852bb0f49a61a7503024',
    messagingSenderId: '571328978605',
    projectId: 'gymgo-5f30f',
    storageBucket: 'gymgo-5f30f.firebasestorage.app',
    iosBundleId: 'com.example.gymgo',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDciMEv14G1pYQjvCL8cfgC8nI0JMBMUzA',
    appId: '1:571328978605:web:d6d838215f7ae02e503024',
    messagingSenderId: '571328978605',
    projectId: 'gymgo-5f30f',
    authDomain: 'gymgo-5f30f.firebaseapp.com',
    storageBucket: 'gymgo-5f30f.firebasestorage.app',
    measurementId: 'G-WX0C5XLNQ8',
  );
}
