import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import '../schedule/gym_class.dart';
import '../service/schedule_service.dart'; // Assuming ScheduleService is available or you have direct Firestore access

class EditClassScreen extends StatefulWidget {
  final GymClass gymClass; // The class to edit

  const EditClassScreen({super.key, required this.gymClass});

  @override
  _EditClassScreenState createState() => _EditClassScreenState();
}

class _EditClassScreenState extends State<EditClassScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _instructorController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final TextEditingController _durationController = TextEditingController();
  final TextEditingController _capacityController = TextEditingController();
  final TextEditingController _enrolledController = TextEditingController();
  final TextEditingController _paymentController = TextEditingController(); // Payment controller

  DateTime? _selectedDate;
  String? _adminName; // To potentially update instructor if needed, or just display

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final ScheduleService _scheduleService = ScheduleService(); // Assuming ScheduleService for updating

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize controllers with existing class data
    _nameController.text = widget.gymClass.name;
    _instructorController.text = widget.gymClass.instructor;
    _selectedDate = widget.gymClass.date;
    _timeController.text = widget.gymClass.time;
    _durationController.text = widget.gymClass.duration;
    _capacityController.text = widget.gymClass.capacity.toString();
    _enrolledController.text = widget.gymClass.enrolled.toString();
    _paymentController.text = widget.gymClass.payment?.toString() ?? ''; // Initialize payment

    _loadAdminName(); // Load admin name if needed for instructor field
  }

  Future<void> _loadAdminName() async {
     final user = _auth.currentUser;
     if (user != null) {
       final doc = await _firestore.collection('users').doc(user.uid).get();
       if (mounted) {
         setState(() {
           _adminName = doc['name'] as String?;
         });
       }
     }
   }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final updatedClass = widget.gymClass.copyWith(
          name: _nameController.text,
          // Optionally update instructor if needed, using _adminName
          // instructor: _adminName ?? widget.gymClass.instructor,
          date: _selectedDate, // Use the potentially updated date
          time: _timeController.text,
          duration: _durationController.text,
          capacity: int.parse(_capacityController.text), // Parse to int
          enrolled: int.parse(_enrolledController.text), // Parse to int
          payment: double.tryParse(_paymentController.text), // Parse payment to double
        );

        // Assuming you have an updateClass method in ScheduleService
        await _scheduleService.updateClass(updatedClass);

        if (mounted) {
          Navigator.pop(context); // Go back after saving
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Class updated successfully!')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating class: ${e.toString()}')),
          );
        }
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit ${widget.gymClass.name}'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: ListView(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(labelText: 'Class Name'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a class name';
                        }
                        return null;
                      },
                    ),
                    // Add more fields for editing (instructor, time, duration, capacity, enrolled)
                    TextFormField(
                      controller: _instructorController,
                      decoration: const InputDecoration(labelText: 'Instructor'),
                       validator: (value) {
                         if (value == null || value.isEmpty) {
                           return 'Please enter instructor name';
                         }
                         return null;
                       },
                    ),
                     InkWell(
                       onTap: () => _selectDate(context),
                       child: InputDecorator(
                         decoration: const InputDecoration(labelText: 'Date'),
                         child: Text(
                            _selectedDate != null
                                ? DateFormat('yyyy-MM-dd').format(_selectedDate!)
                                : 'Select date',
                         ),
                       ),
                     ),
                     TextFormField(
                       controller: _timeController,
                       decoration: const InputDecoration(labelText: 'Time'),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter class time';
                          }
                          return null;
                        },
                     ),
                     TextFormField(
                       controller: _durationController,
                       decoration: const InputDecoration(labelText: 'Duration'),
                         validator: (value) {
                           if (value == null || value.isEmpty) {
                             return 'Please enter class duration';
                           }
                           return null;
                         },
                     ),
                     TextFormField(
                       controller: _capacityController,
                       decoration: const InputDecoration(labelText: 'Capacity'),
                       keyboardType: TextInputType.number,
                         validator: (value) {
                           if (value == null || value.isEmpty) {
                             return 'Please enter class capacity';
                           }
                            if (int.tryParse(value) == null) {
                             return 'Please enter a valid number';
                           }
                           return null;
                         },
                     ),
                      TextFormField(
                       controller: _enrolledController,
                       decoration: const InputDecoration(labelText: 'Enrolled'),
                       keyboardType: TextInputType.number,
                         validator: (value) {
                           if (value == null || value.isEmpty) {
                             return 'Please enter enrolled count';
                           }
                            if (int.tryParse(value) == null) {
                             return 'Please enter a valid number';
                           }
                           return null;
                         },
                     ),
                    // Payment TextField
                    TextFormField(
                      controller: _paymentController,
                      decoration: const InputDecoration(
                        labelText: 'Payment',
                        hintText: 'Enter payment amount',
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                       validator: (value) {
                          if (value != null && value.isNotEmpty && double.tryParse(value) == null) {
                            return 'Please enter a valid number for payment';
                          }
                          return null; // Payment is optional, so null is allowed if empty
                       },
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _saveChanges,
                      child: const Text('Save Changes'),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
} 