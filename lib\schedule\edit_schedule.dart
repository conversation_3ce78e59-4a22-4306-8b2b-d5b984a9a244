import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'gym_class.dart';
import '../service/schedule_service.dart';

class EditScheduleDialog extends StatefulWidget {
  final GymClass gymClass;
  final Function(GymClass) onEditClass;

  const EditScheduleDialog({
    super.key,
    required this.gymClass,
    required this.onEditClass,
  });

  static void show(BuildContext context, ScheduleService scheduleService, GymClass gymClass) {
    showDialog(
      context: context,
      builder: (context) => EditScheduleDialog(
        gymClass: gymClass,
        onEditClass: (GymClass updatedClass) async {
          if (updatedClass.id == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Error: Class ID is missing'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 2),
              ),
            );
            return;
          }
          
          try {
            // Check for existing classes at the same date and time (excluding the current class)
            final existingClasses = await FirebaseFirestore.instance
                .collection('gym_classes')
                .where('date', isEqualTo: updatedClass.date.toIso8601String())
                .where('time', isEqualTo: updatedClass.time)
                .get();

            if (existingClasses.docs.isNotEmpty) {
              // Check if the found class is not the current class being edited
              final isOtherClass = existingClasses.docs.any((doc) => doc.id != updatedClass.id);
              if (isOtherClass) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('A class already exists at this date and time!'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
                return;
              }
            }

            await scheduleService.updateClass(updatedClass);
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Class updated successfully!'),
                  backgroundColor: Color.fromARGB(255, 205, 148, 104),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error updating class: ${e.toString()}'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        },
      ),
    );
  }

  @override
  State<EditScheduleDialog> createState() => _EditScheduleDialogState();
}

class _EditScheduleDialogState extends State<EditScheduleDialog> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController paymentController = TextEditingController();
  final TextEditingController capacityController = TextEditingController();
  late String selectedTime;
  late String selectedDuration;
  late DateTime? selectedDate;

  // Add error message states
  String? nameError;
  String? timeError;
  String? durationError;
  String? capacityError;
  String? dateError;
  String? paymentError;

  final List<String> timeOptions = [
    'Select time',
    '08:00 AM', '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM', '06:00 PM',
    '07:00 PM', '08:00 PM'
  ];

  final List<String> durationOptions = [
    'Select duration',
    '30 min', '40 min', '50 min', '60 min', '90 min'
  ];

  @override
  void initState() {
    super.initState();
    // Initialize controllers with existing values
    nameController.text = widget.gymClass.name;
    paymentController.text = widget.gymClass.payment?.toString() ?? '';
    capacityController.text = widget.gymClass.capacity.toString();
    
    // Ensure the selected values exactly match the dropdown options
    selectedTime = timeOptions.contains(widget.gymClass.time) 
        ? widget.gymClass.time 
        : timeOptions[0];
        
    selectedDuration = durationOptions.contains(widget.gymClass.duration)
        ? widget.gymClass.duration
        : durationOptions[0];
        
    selectedDate = widget.gymClass.date;
  }

  @override
  void dispose() {
    nameController.dispose();
    paymentController.dispose();
    capacityController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color.fromARGB(255, 205, 148, 104),
              onPrimary: Colors.white,
              surface: Color.fromARGB(255, 239, 232, 222),
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
      helpText: '',
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        dateError = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Edit Class',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 70, 70, 70),
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'Class Name',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorText: nameError,
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.fitness_center, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
              ),
              const SizedBox(height: 16),
              // Payment TextField styled like Class Name
              TextField(
                controller: paymentController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Payment',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.attach_money, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Date',
                    labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                    errorText: dateError,
                    errorStyle: const TextStyle(color: Colors.red),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                    ),
                    prefixIcon: const Icon(Icons.calendar_today, color: Color.fromARGB(255, 211, 169, 137)),
                    suffixIcon: const Icon(Icons.arrow_drop_down, color: Color.fromARGB(255, 109, 109, 109)),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  child: Text(
                    selectedDate != null
                        ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                        : 'Select date',
                    style: TextStyle(
                      color: selectedDate != null 
                          ? Colors.black 
                          : const Color.fromARGB(255, 109, 109, 109),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedTime,
                decoration: InputDecoration(
                  labelText: 'Time',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorText: timeError,
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.access_time, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
                dropdownColor: Colors.white,
                menuMaxHeight: 200,
                items: timeOptions.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: value == 'Select time' 
                            ? const Color.fromARGB(255, 109, 109, 109)
                            : Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedTime = newValue!;
                    timeError = null;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedDuration,
                decoration: InputDecoration(
                  labelText: 'Duration',
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  errorText: durationError,
                  errorStyle: const TextStyle(color: Colors.red),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.timer, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
                dropdownColor: Colors.white,
                menuMaxHeight: 200,
                items: durationOptions.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: value == 'Select duration' 
                            ? const Color.fromARGB(255, 109, 109, 109)
                            : Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedDuration = newValue!;
                    durationError = null;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: capacityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Capacity',
                  errorText: capacityError,
                  labelStyle: const TextStyle(color: Color.fromARGB(255, 109, 109, 109)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Color.fromARGB(255, 211, 169, 137), width: 2),
                  ),
                  prefixIcon: const Icon(Icons.people, color: Color.fromARGB(255, 211, 169, 137)),
                ),
                style: const TextStyle(color: Colors.black),
                onChanged: (value) {
                  setState(() {
                    capacityError = null;
                  });
                },
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: const Color.fromARGB(255, 109, 109, 109),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      // Reset all error messages
                      setState(() {
                        nameError = null;
                        timeError = null;
                        durationError = null;
                        capacityError = null;
                        dateError = null;
                        paymentError = null;
                      });

                      // Validate each field
                      bool isValid = true;
                      if (nameController.text.isEmpty) {
                        setState(() {
                          nameError = 'Please fill in class name';
                        });
                        isValid = false;
                      }
                      if (selectedDate == null) {
                        setState(() {
                          dateError = 'Please select a date';
                        });
                        isValid = false;
                      }
                      if (selectedTime == 'Select time') {
                        setState(() {
                          timeError = 'Please select a time';
                        });
                        isValid = false;
                      }
                      if (selectedDuration == 'Select duration') {
                        setState(() {
                          durationError = 'Please select a duration';
                        });
                        isValid = false;
                      }
                      if (capacityController.text.isEmpty) {
                        setState(() {
                          capacityError = 'Please enter capacity';
                        });
                        isValid = false;
                      } else {
                        final capacity = int.tryParse(capacityController.text);
                        if (capacity == null || capacity <= 0) {
                          setState(() {
                            capacityError = 'Please enter a valid positive number';
                          });
                          isValid = false;
                        }
                      }

                      // Validate payment
                      if (!_validatePayment()) {
                        isValid = false;
                      }

                      if (isValid) {
                        final updatedClass = GymClass(
                          id: widget.gymClass.id,
                          name: nameController.text,
                          instructor: widget.gymClass.instructor,
                          date: selectedDate!,
                          time: selectedTime,
                          duration: selectedDuration,
                          capacity: int.parse(capacityController.text),
                          enrolled: widget.gymClass.enrolled,
                          payment: double.tryParse(paymentController.text),
                        );
                        
                        widget.onEditClass(updatedClass);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 211, 169, 137),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _validatePayment() {
    double? paymentValue;
    if (paymentController.text.isNotEmpty) {
      paymentValue = double.tryParse(paymentController.text);
      if (paymentValue == null) {
        setState(() {
          paymentError = 'Please enter a valid number for payment';
        });
        return false;
      }
    }
    return true;
  }
}