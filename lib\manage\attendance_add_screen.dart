import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:gymgo/Service/attendance_service.dart';

class AttendanceAddScreen extends StatefulWidget {
  final String classId;
  final String className;
  final DateTime classDate;

  const AttendanceAddScreen({
    super.key,
    required this.classId,
    required this.className,
    required this.classDate,
  });

  @override
  _AttendanceAddScreenState createState() => _AttendanceAddScreenState();
}

class _AttendanceAddScreenState extends State<AttendanceAddScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final AttendanceService _attendanceService = AttendanceService();

  String? _selectedChildId;
  String _selectedStatus = 'Present';
  final TextEditingController _notesController = TextEditingController();

  List<Map<String, dynamic>> _users = [];
  Set<String> _childrenWithAttendance = {};

  DateTime _currentCheckInTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadChildren();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadChildren() async {
    try {
      final allBookedChildren = await _attendanceService.getChildrenBookedForClass(widget.classId);

      final DateTime normalizedDate = DateTime(
        widget.classDate.year,
        widget.classDate.month,
        widget.classDate.day,
      );

      final attendanceSnapshot = await _firestore
          .collection('attendance')
          .where('classId', isEqualTo: widget.classId)
          .where('date', isEqualTo: Timestamp.fromDate(normalizedDate))
          .get();

      Set<String> childrenWithAttendance = {};
      for (var doc in attendanceSnapshot.docs) {
        final data = doc.data();
        if (data.containsKey('userId') && data['userId'] is String) {
          childrenWithAttendance.add(data['userId'] as String);
        }
      }

      final eligibleChildren = allBookedChildren.where((child) {
        final childId = child['childId'];
        return childId != null && !childrenWithAttendance.contains(childId);
      }).toList();

      setState(() {
        _users = eligibleChildren;
        _childrenWithAttendance = childrenWithAttendance;
        _selectedChildId = null;
      });

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading children or attendance: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _addAttendance() async {
    if (_selectedChildId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a child'),
          backgroundColor: Color.fromARGB(255, 205, 148, 104),
        ),
      );
      return;
    }

    try {
      final DateTime normalizedDate = DateTime(
        widget.classDate.year,
        widget.classDate.month,
        widget.classDate.day,
      );

      final QuerySnapshot existingAttendanceQuery = await _firestore
          .collection('attendance')
          .where('classId', isEqualTo: widget.classId)
          .where('userId', isEqualTo: _selectedChildId)
          .where('date', isEqualTo: Timestamp.fromDate(normalizedDate))
          .get();

      if (existingAttendanceQuery.docs.isNotEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Attendance record already exists for this child today'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        return;
      }

      final selectedChild = _users.firstWhere((c) => c['childId'] == _selectedChildId);

      final DocumentReference attendanceRef = _firestore.collection('attendance').doc();

      final Map<String, dynamic> attendanceData = {
        'id': attendanceRef.id,
        'classId': widget.classId,
        'className': widget.className,
        'userId': _selectedChildId,
        'userName': '${selectedChild['childName']} (Parent: ${selectedChild['parentName']})',
        'status': _selectedStatus,
        'date': Timestamp.fromDate(normalizedDate),
        'checkInTime': Timestamp.fromDate(_currentCheckInTime),
        'notes': _notesController.text,
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': _auth.currentUser?.uid,
      };

      await _firestore.runTransaction((transaction) async {
        final snapshot = await transaction.get(attendanceRef);
        if (snapshot.exists) {
          throw Exception('Duplicate attendance record');
        }
        transaction.set(attendanceRef, attendanceData);
      });

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Attendance record added successfully'),
            backgroundColor: Color.fromARGB(255, 205, 148, 104),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding attendance: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<TimeOfDay?> _showCustomTimePicker(BuildContext context, TimeOfDay initialTime) async {
    int hour = initialTime.hourOfPeriod;
    int minute = initialTime.minute;
    bool isPM = initialTime.period == DayPeriod.pm;
    final hourController = TextEditingController(text: hour.toString().padLeft(2, '0'));
    final minuteController = TextEditingController(text: minute.toString().padLeft(2, '0'));
    bool confirmed = false;
    String? errorText;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
              title: const Text('Choose Time', style: TextStyle(fontWeight: FontWeight.bold)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 70,
                        child: TextField(
                          controller: hourController,
                          keyboardType: TextInputType.number,
                          maxLength: 2,
                          decoration: InputDecoration(
                            labelText: 'Hour',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                            counterText: '',
                          ),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text(':', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                      ),
                      SizedBox(
                        width: 70,
                        child: TextField(
                          controller: minuteController,
                          keyboardType: TextInputType.number,
                          maxLength: 2,
                          decoration: InputDecoration(
                            labelText: 'Minute',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                            counterText: '',
                          ),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ToggleButtons(
                    isSelected: [!isPM, isPM],
                    borderRadius: BorderRadius.circular(8),
                    selectedColor: Colors.white,
                    fillColor: const Color(0xFF8B5E3C),
                    color: const Color(0xFF8B5E3C),
                    children: const [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
                        child: Text('AM', style: TextStyle(fontSize: 16)),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
                        child: Text('PM', style: TextStyle(fontSize: 16)),
                      ),
                    ],
                    onPressed: (index) {
                      setState(() {
                        isPM = index == 1;
                      });
                    },
                  ),
                  if (errorText != null) ...[
                    const SizedBox(height: 12),
                    Text(errorText!, style: const TextStyle(color: Colors.red, fontSize: 13)),
                  ],
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF8B5E3C),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  onPressed: () {
                    int? enteredHour = int.tryParse(hourController.text);
                    int? enteredMinute = int.tryParse(minuteController.text);
                    if (enteredHour == null || enteredMinute == null || enteredHour < 1 || enteredHour > 12 || enteredMinute < 0 || enteredMinute > 59) {
                      setState(() {
                        errorText = 'Please enter a valid time (1-12 for hour, 0-59 for minute).';
                      });
                      return;
                    }
                    confirmed = true;
                    Navigator.of(context).pop();
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );

    if (!confirmed) return null;
    int? enteredHour = int.tryParse(hourController.text);
    int? enteredMinute = int.tryParse(minuteController.text);
    if (enteredHour == null || enteredMinute == null || enteredHour < 1 || enteredHour > 12 || enteredMinute < 0 || enteredMinute > 59) {
      return null;
    }
    int finalHour = enteredHour % 12 + (isPM ? 12 : 0);
    return TimeOfDay(hour: finalHour, minute: enteredMinute);
  }

  Widget _buildSectionCard({required String title, required List<Widget> children}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF8B5E3C),
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildStaticInfoRow({required IconData icon, required String label, required String value}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 15),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFD3A989)),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Icon(icon, color: const Color(0xFF8B5E3C), size: 20),
          const SizedBox(width: 12),
          Column(
             crossAxisAlignment: CrossAxisAlignment.start,
             children: [
                Text(
                   label,
                   style: const TextStyle(fontSize: 12, color: Colors.black54),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(fontSize: 16, color: Colors.black),
                ),
             ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: const Text(
          'Add Attendance',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF8B5E3C),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionCard(
              title: 'Class Information',
              children: [
                _buildStaticInfoRow(
                   icon: Icons.fitness_center,
                   label: 'Class',
                   value: widget.className,
                ),
                const SizedBox(height: 16),
                _buildStaticInfoRow(
                   icon: Icons.calendar_today,
                   label: 'Date',
                   value: DateFormat('MM/dd/yyyy').format(widget.classDate),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSectionCard(
              title: 'User Information',
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedChildId,
                  decoration: InputDecoration(
                    labelText: 'Select Child',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                  items: _users.map<DropdownMenuItem<String>>((child) {
                    return DropdownMenuItem<String>(
                      value: child['childId'] as String,
                      child: Text('${child['childName']} (Parent: ${child['parentName']})'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedChildId = value;
                    });
                  },
                  isDense: true,
                  isExpanded: true,
                ),
                if (_users.isEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'No children booked for this class.',
                    style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic, color: Colors.grey),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 20),
            _buildSectionCard(
              title: 'Attendance Details',
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                  items: ['Present', 'Absent', 'Late']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                  isDense: true,
                  isExpanded: true,
                ),
                const SizedBox(height: 16),
                if (_selectedStatus.toLowerCase() != 'absent')
                  TextFormField(
                    readOnly: true,
                    controller: TextEditingController(text: DateFormat('hh:mm a').format(_currentCheckInTime)),
                    decoration: InputDecoration(
                      labelText: 'Check-in Time',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                      ),
                      prefixIcon: const Icon(Icons.access_time, color: Color(0xFF8B5E3C)),
                    ),
                    onTap: () async {
                      final picked = await _showCustomTimePicker(context, TimeOfDay.fromDateTime(_currentCheckInTime));
                      if (picked != null) {
                        setState(() {
                          _currentCheckInTime = DateTime(
                            _currentCheckInTime.year,
                            _currentCheckInTime.month,
                            _currentCheckInTime.day,
                            picked.hour,
                            picked.minute,
                          );
                        });
                      }
                    },
                  ),
                if (_selectedStatus.toLowerCase() != 'absent') const SizedBox(height: 16),
                TextField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF8B5E3C)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 30),
            Center(
              child: ElevatedButton(
                onPressed: _addAttendance,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF8B5E3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 3,
                ),
                child: const Text('Add Attendance'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 