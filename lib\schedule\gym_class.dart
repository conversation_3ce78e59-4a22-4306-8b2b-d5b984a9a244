class GymClass {
  final String? id; // Document ID from Firebase
  final String name;
  final String instructor;
  final DateTime date;
  final String time;
  final String duration;
  final int capacity;
  final int enrolled;
  final double? payment;

  GymClass({
    this.id,
    required this.name,
    required this.instructor,
    required this.date,
    required this.time,
    required this.duration,
    required this.capacity,
    required this.enrolled,
    this.payment,
  });

  // Create a copy of the class with updated fields
  GymClass copyWith({
    String? id,
    String? name,
    String? instructor,
    DateTime? date,
    String? time,
    String? duration,
    int? capacity,
    int? enrolled,
    double? payment,
  }) {
    return GymClass(
      id: id ?? this.id,
      name: name ?? this.name,
      instructor: instructor ?? this.instructor,
      date: date ?? this.date,
      time: time ?? this.time,
      duration: duration ?? this.duration,
      capacity: capacity ?? this.capacity,
      enrolled: enrolled ?? this.enrolled,
      payment: payment ?? this.payment,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'instructor': instructor,
      'date': date.toIso8601String(),
      'time': time,
      'duration': duration,
      'capacity': capacity,
      'enrolled': enrolled,
      'payment': payment,
    };
  }

  factory GymClass.fromMap(String id, Map<String, dynamic> map) {
    final paymentRaw = map['payment'];
    double? payment;
    if (paymentRaw is int) {
      payment = paymentRaw.toDouble();
    } else if (paymentRaw is double) {
      payment = paymentRaw;
    } else {
      payment = null;
    }
    return GymClass(
      id: id,
      name: map['name'] ?? '',
      instructor: map['instructor'] ?? '',
      date: DateTime.parse(map['date'] ?? DateTime.now().toIso8601String()),
      time: map['time'] ?? '',
      duration: map['duration'] ?? '',
      capacity: map['capacity']?.toInt() ?? 0,
      enrolled: map['enrolled']?.toInt() ?? 0,
      payment: payment,
    );
  }
} 