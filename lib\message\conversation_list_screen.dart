import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:gymgo/Service/message_service.dart';
import 'package:gymgo/message/chat_screen.dart';

class ConversationListScreen extends StatefulWidget {
  const ConversationListScreen({super.key});

  @override
  State<ConversationListScreen> createState() => _ConversationListScreenState();
}

class _ConversationListScreenState extends State<ConversationListScreen> {
  final MessageService _messageService = MessageService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;
  Map<String, Map<String, dynamic>> _userCache = {};
  bool _isLoadingUsers = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
    // Load all users data when screen initializes
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoadingUsers = true;
    });
    final usersData = await _messageService.getAllUsersData();
    if (mounted) {
      setState(() {
        _userCache = usersData;
        _isLoadingUsers = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildUserList(Stream<QuerySnapshot<Map<String, dynamic>>> userStream, bool isAdmin) {
    return StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
      stream: userStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error loading users: ${snapshot.error}'));
        }

        final users = snapshot.data?.docs ?? [];
        final currentUserId = _auth.currentUser?.uid;

        if (users.isEmpty) {
          return Center(
            child: Text(
              'No ${isAdmin ? 'admins' : 'users'} available.',
              style: const TextStyle(
                color: Color(0xFF8B5E3C),
                fontSize: 16,
              ),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: users.length,
          itemBuilder: (context, index) {
            final userDoc = users[index];
            final userData = userDoc.data();
            final userId = userDoc.id;
            
            // Skip current user
            if (userId == currentUserId) return const SizedBox.shrink();

            final userName = userData['name'] ?? userData['fullName'] ?? 'Unknown User';
            final userRole = userData['role'] ?? 'User';
            final avatarPath = userData['avatarPath'] ?? 'assets/user_avatar/user.png';

            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(16),
                  leading: Stack(
                    children: [
                      CircleAvatar(
                        radius: 28,
                        backgroundColor: const Color(0xFFF9EBD7),
                        backgroundImage: AssetImage(avatarPath),
                      ),
                    ],
                  ),
                  title: Text(
                    userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  subtitle: Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: isAdmin ? const Color(0xFF8B5E3C) : const Color(0xFFF9EBD7),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isAdmin ? const Color(0xFF8B5E3C) : const Color(0xFF8B5E3C).withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            userRole,
                            style: TextStyle(
                              color: isAdmin ? Colors.white : const Color(0xFF8B5E3C),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  trailing: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5E3C),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.message, color: Colors.white, size: 20),
                      onPressed: () async {
                        try {
                          await _messageService.startNewChat(userId);
                          if (mounted) {
                            final chatId = await _messageService.getChatId(_auth.currentUser!.uid, userId);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ChatScreen(
                                  chatId: chatId,
                                  otherUserId: userId,
                                  otherUserName: userName,
                                ),
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error starting chat: $e'),
                                backgroundColor: const Color(0xFF8B5E3C),
                              ),
                            );
                          }
                        }
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildConversationList(Stream<List<DocumentSnapshot<Map<String, dynamic>>>> conversationsStream) {
    if (_isLoadingUsers) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5E3C)),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading conversations...',
              style: TextStyle(
                color: const Color(0xFF8B5E3C).withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return StreamBuilder<List<DocumentSnapshot<Map<String, dynamic>>>>(
      stream: conversationsStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8B5E3C)),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading conversations...',
                  style: TextStyle(
                    color: const Color(0xFF8B5E3C).withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        } else if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  'Error loading conversations: ${snapshot.error}',
                  style: TextStyle(color: Colors.red[300]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final conversations = snapshot.data ?? [];

        if (conversations.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: const Color(0xFF8B5E3C).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.chat_bubble_outline,
                    size: 64,
                    color: Color(0xFF8B5E3C),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'No conversations yet',
                  style: TextStyle(
                    color: Color(0xFF8B5E3C),
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'Start chatting with other users by searching for them using the search icon above',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _isSearching = true;
                    });
                  },
                  icon: const Icon(Icons.search),
                  label: const Text('Find Users'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF8B5E3C),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.only(top: 8),
          itemCount: conversations.length,
          itemBuilder: (context, index) {
            final conversation = conversations[index].data() as Map<String, dynamic>?;
            if (conversation == null) {
              return const SizedBox.shrink();
            }

            final chatId = conversations[index].id;
            final participants = conversation['participants'] as List<dynamic>?;
            final currentUserId = _auth.currentUser?.uid;

            final otherUserId = (participants != null && currentUserId != null)
                ? participants.firstWhere(
                    (uid) => uid != currentUserId,
                    orElse: () => null,
                  )
                : null;

            if (otherUserId == null) {
              return const SizedBox.shrink();
            }

            final lastMessage = conversation['lastMessage'] ?? '';
            final lastMessageTimestamp = conversation['lastMessageTimestamp'] as Timestamp?;
            final unreadCounts = conversation['unreadCounts'] as Map<String, dynamic>? ?? {};
            final unreadCount = unreadCounts[currentUserId] as int? ?? 0;

            // Get user data from cache
            final userData = _userCache[otherUserId.toString()];
            if (userData == null) {
              // If user data is not in cache, trigger a reload
              _loadUserData();
              return const SizedBox.shrink();
            }

            final otherUserName = userData['name']?.toString() ?? userData['fullName']?.toString() ?? 'Unknown User';
            final avatarPath = userData['avatarPath']?.toString() ?? 'assets/user_avatar/user.png';
            final userRole = userData['role']?.toString() ?? 'User';

            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(16),
                  leading: CircleAvatar(
                    radius: 28,
                    backgroundColor: const Color(0xFFF9EBD7),
                    backgroundImage: AssetImage(avatarPath),
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Text(
                              otherUserName,
                              style: TextStyle(
                                fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                                fontSize: 16,
                                color: const Color(0xFF8B5E3C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: userRole == 'Admin' ? const Color(0xFF8B5E3C) : const Color(0xFFF9EBD7),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: userRole == 'Admin' ? const Color(0xFF8B5E3C) : const Color(0xFF8B5E3C).withOpacity(0.3),
                                ),
                              ),
                              child: Text(
                                userRole,
                                style: TextStyle(
                                  color: userRole == 'Admin' ? Colors.white : const Color(0xFF8B5E3C),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (lastMessageTimestamp != null)
                        Text(
                          _formatTimestamp(lastMessageTimestamp),
                          style: TextStyle(
                            color: const Color(0xFF8B5E3C).withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                  subtitle: Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            lastMessage.toString(),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                              fontSize: 14,
                              color: const Color(0xFF8B5E3C).withOpacity(0.7),
                            ),
                          ),
                        ),
                        if (unreadCount > 0)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatScreen(
                          chatId: chatId,
                          otherUserId: otherUserId.toString(),
                          otherUserName: otherUserName,
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  String _formatTimestamp(Timestamp timestamp) {
    final now = DateTime.now().toLocal();
    final date = timestamp.toDate().toLocal();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      // Today, show time in 12-hour format with AM/PM
      final hour = date.hour;
      final minute = date.minute;
      final period = hour < 12 ? 'AM' : 'PM';
      final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
      return '$hour12:${minute.toString().padLeft(2, '0')} $period';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // Within a week, show day name
      return _getDayName(date.weekday);
    } else {
      // Show date in DD/MM/YYYY format
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case DateTime.monday: return 'Mon';
      case DateTime.tuesday: return 'Tue';
      case DateTime.wednesday: return 'Wed';
      case DateTime.thursday: return 'Thu';
      case DateTime.friday: return 'Fri';
      case DateTime.saturday: return 'Sat';
      case DateTime.sunday: return 'Sun';
      default: return '';
    }
  }

  Widget _buildUserSearchList(String query) {
    if (query.isEmpty) {
      return const Center(
        child: Text(
          'Search for users by name',
          style: TextStyle(
            color: Color(0xFF8B5E3C),
            fontSize: 16,
          ),
        ),
      );
    }

    return StreamBuilder<List<DocumentSnapshot<Map<String, dynamic>>>>(
      stream: _messageService.searchUsersByName(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error searching users: ${snapshot.error}'));
        }

        final users = snapshot.data ?? [];
        final currentUserId = _auth.currentUser?.uid;

        // Filter out current user
        final filteredUsers = users.where((userDoc) => userDoc.id != currentUserId).toList();

        if (filteredUsers.isEmpty) {
          return const Center(
            child: Text(
              'No users found',
              style: TextStyle(
                color: Color(0xFF8B5E3C),
                fontSize: 16,
              ),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: filteredUsers.length,
          itemBuilder: (context, index) {
            final userDoc = filteredUsers[index];
            final userData = userDoc.data() as Map<String, dynamic>? ?? {};
            final userId = userDoc.id;
            final userName = userData['name']?.toString() ?? userData['fullName']?.toString() ?? 'Unknown User';
            final userRole = userData['role']?.toString() ?? 'User';
            final avatarPath = userData['avatarPath']?.toString() ?? 'assets/user_avatar/user.png';

            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(16),
                  leading: CircleAvatar(
                    radius: 28,
                    backgroundColor: const Color(0xFFF9EBD7),
                    backgroundImage: AssetImage(avatarPath),
                  ),
                  title: Text(
                    userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF8B5E3C),
                    ),
                  ),
                  subtitle: Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: userRole == 'Admin' ? const Color(0xFF8B5E3C) : const Color(0xFFF9EBD7),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: userRole == 'Admin' ? const Color(0xFF8B5E3C) : const Color(0xFF8B5E3C).withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            userRole,
                            style: TextStyle(
                              color: userRole == 'Admin' ? Colors.white : const Color(0xFF8B5E3C),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () async {
                    try {
                      await _messageService.startNewChat(userId);
                      if (mounted) {
                        final chatId = await _messageService.getChatId(_auth.currentUser!.uid, userId);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ChatScreen(
                              chatId: chatId,
                              otherUserId: userId,
                              otherUserName: userName,
                            ),
                          ),
                        );
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error starting chat: $e'),
                            backgroundColor: const Color(0xFF8B5E3C),
                          ),
                        );
                      }
                    }
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _auth.currentUser;

    if (currentUser == null) {
      return Scaffold(
        backgroundColor: const Color(0xFFF9EBD7),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: const Color(0xFF8B5E3C).withOpacity(0.7),
              ),
              const SizedBox(height: 16),
              const Text(
                'Please log in to view messages',
                style: TextStyle(
                  color: Color(0xFF8B5E3C),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7),
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search users...',
                  hintStyle: const TextStyle(color: Colors.white70),
                  border: InputBorder.none,
                  prefixIcon: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _isSearching = false;
                        _searchController.clear();
                      });
                    },
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.white),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                ),
                style: const TextStyle(color: Colors.white, fontSize: 16),
                cursorColor: Colors.white,
                autofocus: true,
              )
            : Row(
                children: [
                  const Icon(
                    Icons.chat_bubble_outline,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 10),
                  const Text(
                    'Messages',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
        backgroundColor: const Color(0xFF8B5E3C),
        elevation: 0,
        centerTitle: false,
        actions: [
          if (!_isSearching)
            IconButton(
              icon: const Icon(Icons.search, color: Colors.white),
              onPressed: () {
                setState(() {
                  _isSearching = true;
                });
              },
            ),
        ],
      ),
      body: _isSearching
          ? _buildUserSearchList(_searchQuery)
          : _buildConversationList(_messageService.getConversations()),
    );
  }
} 
