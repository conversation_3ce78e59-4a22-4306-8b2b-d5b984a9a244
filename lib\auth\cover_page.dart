import 'package:flutter/material.dart';
import 'package:gymgo/auth/login_screen.dart';

class CoverPage extends StatefulWidget {
  const CoverPage({super.key});

  @override
  _CoverPageState createState() => _CoverPageState();
}

class _CoverPageState extends State<CoverPage> {
  @override
  void initState() {
    super.initState();
    // Delay for 3 seconds before navigating to LoginScreen
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9EBD7), // Background color
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(50), // Added padding
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/logonametrans.png",
                height: 300, // App logo
              ),
              const SizedBox(height: 20),
              const LinearProgressIndicator(
                color: Colors.brown, // Loading spinner
              ),
            ],
          ),
        ),
      ),
    );
  }
}
