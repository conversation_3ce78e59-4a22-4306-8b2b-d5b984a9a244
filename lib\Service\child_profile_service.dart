import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ChildProfileService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Create a new child profile in the top-level /children collection
  Future<DocumentReference> createChildProfile({
    required String fullName,
    required DateTime birthDate,
    required String gender,
    String? avatarPath,
  }) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) throw Exception('User not authenticated');

    return await _firestore.collection('children').add({
      'fullName': fullName,
      'birthDate': Timestamp.fromDate(birthDate),
      'gender': gender,
      'avatarPath': avatarPath,
      'userId': userId,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  // Get all child profiles for the current user from the top-level /children collection
  Stream<QuerySnapshot> getChildProfiles() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) throw Exception('User not authenticated');
    return _firestore
        .collection('children')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  // Get a specific child profile from the top-level /children collection
  Future<DocumentSnapshot> getChildProfile(String childId) async {
    try {
      return await _firestore.collection('children').doc(childId).get();
    } catch (e) {
      throw Exception('Failed to get child profile: $e');
    }
  }

  // Update a child profile in the top-level /children collection
  Future<void> updateChildProfile({
    required String childId,
    required String fullName,
    required DateTime birthDate,
    required String gender,
    String? avatarPath,
  }) async {
    try {
      await _firestore.collection('children').doc(childId).update({
        'fullName': fullName,
        'birthDate': Timestamp.fromDate(birthDate),
        'gender': gender,
        'avatarPath': avatarPath,
      });
    } catch (e) {
      throw Exception('Failed to update child profile: $e');
    }
  }

  // Delete a child profile from the top-level /children collection
  Future<void> deleteChildProfile(String childId) async {
    try {
      await _firestore.collection('children').doc(childId).delete();
    } catch (e) {
      throw Exception('Failed to delete child profile: $e');
    }
  }
} 